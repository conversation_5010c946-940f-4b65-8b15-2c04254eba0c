import sys
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QComboBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap
import cv2
import requests


class VideoThread(QThread):
    change_pixmap = pyqtSignal(QImage)
    update_counts = pyqtSignal(dict)

    def __init__(self, rtmp_url, api_url):
        super().__init__()
        self.rtmp_url = rtmp_url
        self.api_url = api_url
        self.running = False
        self.cap = None

    def validate_and_process_frame(self, frame):
        """验证和预处理帧，防止花屏"""
        try:
            # 检查帧的基本属性
            if frame is None:
                return None

            if len(frame.shape) != 3:
                print(f"⚠️ 帧维度错误: {frame.shape}")
                return None

            h, w, c = frame.shape
            if c != 3:
                print(f"⚠️ 帧通道数错误: {c}")
                return None

            if h <= 0 or w <= 0:
                print(f"⚠️ 帧尺寸无效: {w}x{h}")
                return None

            # 检查数据类型
            if frame.dtype != 'uint8':
                frame = frame.astype('uint8')

            # 确保内存连续性
            if not frame.flags['C_CONTIGUOUS']:
                frame = frame.copy()

            return frame

        except Exception as e:
            print(f"⚠️ 帧验证错误: {e}")
            return None

    def run(self):
        """运行视频线程，采用project3的超低延迟RTMP拉流配置"""
        self.running = True

        # project3超低延迟RTMP拉流配置
        print(f"正在连接RTMP流: {self.rtmp_url}")

        # 尝试多种方式连接RTMP流
        connection_methods = [
            # 方法1: 直接使用OpenCV连接
            lambda: self._connect_opencv_direct(),
            # 方法2: 使用FFmpeg参数连接
            lambda: self._connect_opencv_with_params(),
            # 方法3: 使用CAP_FFMPEG后端
            lambda: self._connect_opencv_ffmpeg()
        ]

        for i, method in enumerate(connection_methods):
            try:
                print(f"尝试连接方法 {i+1}...")
                self.cap = method()
                if self.cap and self.cap.isOpened():
                    # 测试读取一帧
                    ret, test_frame = self.cap.read()
                    if ret and test_frame is not None:
                        print(f"连接方法 {i+1} 成功!")
                        break
                    else:
                        print(f"连接方法 {i+1} 无法读取帧")
                        if self.cap:
                            self.cap.release()
                            self.cap = None
                else:
                    print(f"连接方法 {i+1} 失败")
                    if self.cap:
                        self.cap.release()
                        self.cap = None
            except Exception as e:
                print(f"连接方法 {i+1} 异常: {e}")
                if self.cap:
                    self.cap.release()
                    self.cap = None

        if not self.cap or not self.cap.isOpened():
            print("❌ 所有连接方法都失败了！请检查:")
            print("1. RTMP服务器是否正在运行")
            print("2. 服务器是否正在推流")
            print("3. 网络连接是否正常")
            return

        print("✅ RTMP流连接成功，开始接收视频...")
        frame_count = 0

        while self.running:
            ret, frame = self.cap.read()
            if ret and frame is not None:
                frame_count += 1

                # 验证和预处理帧
                processed_frame = self.validate_and_process_frame(frame)
                if processed_frame is not None:
                    try:
                        # 简化的图像转换方法 - 避免花屏
                        # 1. 转换颜色空间
                        rgb_image = cv2.cvtColor(processed_frame, cv2.COLOR_BGR2RGB)
                        h, w, ch = rgb_image.shape

                        # 2. 使用简单的字节计算
                        bytes_per_line = w * ch

                        # 3. 确保数据连续性
                        if not rgb_image.flags['C_CONTIGUOUS']:
                            rgb_image = np.ascontiguousarray(rgb_image)

                        # 4. 创建QImage并立即复制数据
                        qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)

                        # 5. 立即复制图像数据，避免内存引用问题
                        qt_image_copy = qt_image.copy()

                        # 6. 验证图像有效性
                        if not qt_image_copy.isNull() and qt_image_copy.width() > 0 and qt_image_copy.height() > 0:
                            self.change_pixmap.emit(qt_image_copy)
                        else:
                            print("⚠️ QImage创建失败")

                    except Exception as e:
                        print(f"⚠️ 图像转换错误: {e}")
                        continue
                else:
                    print("⚠️ 帧验证失败，跳过此帧")

                # 每30帧获取一次计数
                if frame_count % 30 == 0:
                    self.get_counts()
            else:
                print("⚠️ 读取帧失败，尝试重连...")
                # 尝试重新连接
                if self.cap:
                    self.cap.release()

                # 等待一秒后重试
                self.msleep(1000)

                # 重新连接
                try:
                    self.cap = self._connect_opencv_with_params()
                    if not self.cap or not self.cap.isOpened():
                        print("❌ 重连失败，退出视频线程")
                        break
                except:
                    print("❌ 重连异常，退出视频线程")
                    break

        if self.cap:
            self.cap.release()
        print(f"视频线程结束，共处理 {frame_count} 帧")

    def _connect_opencv_direct(self):
        """方法1: 直接使用OpenCV连接RTMP"""
        return cv2.VideoCapture(self.rtmp_url)

    def _connect_opencv_with_params(self):
        """方法2: 使用project3的低延迟参数连接RTMP"""
        cap = cv2.VideoCapture()

        # project3低延迟参数设置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
        cap.set(cv2.CAP_PROP_FPS, 30)  # 设置期望帧率

        # 尝试打开RTMP流
        if cap.open(self.rtmp_url):
            return cap
        else:
            cap.release()
            return None

    def _connect_opencv_ffmpeg(self):
        """方法3: 使用FFmpeg后端连接RTMP"""
        try:
            return cv2.VideoCapture(self.rtmp_url, cv2.CAP_FFMPEG)
        except:
            return None

    def get_counts(self):
        try:
            response = requests.get(f"{self.api_url}/status")
            if response.status_code == 200:
                data = response.json()
                self.update_counts.emit(data.get('counts', {}))
        except Exception as e:
            print(f"获取计数错误: {e}")

    def stop(self):
        self.running = False
        self.wait()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("电子元器件计数系统")
        self.setGeometry(100, 100, 800, 600)

        # 配置
        self.rtmp_url = "rtmp://localhost/live/stream"
        self.api_url = "http://localhost:5000/api"

        # 初始化UI
        self.init_ui()

        # 视频线程
        self.video_thread = None

        # 状态检查定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_server_status)
        self.status_timer.start(1000)  # 每秒检查一次

    def init_ui(self):
        # 主部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 视频显示区域
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(640, 640)
        self.video_label.setStyleSheet("background-color: black;")
        main_layout.addWidget(self.video_label)

        # 控制面板
        control_panel = QHBoxLayout()

        # 摄像头选择
        self.camera_combo = QComboBox()
        self.camera_combo.addItems(["摄像头0", "摄像头1", "摄像头2"])
        control_panel.addWidget(QLabel("选择摄像头:"))
        control_panel.addWidget(self.camera_combo)

        # 分辨率选择
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["480x480", "640x640", "800x600"])
        control_panel.addWidget(QLabel("分辨率:"))
        control_panel.addWidget(self.resolution_combo)

        # 帧率选择
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["15", "24", "30", "60"])
        control_panel.addWidget(QLabel("帧率:"))
        control_panel.addWidget(self.fps_combo)

        main_layout.addLayout(control_panel)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 开始按钮
        self.start_btn = QPushButton("开始检测")
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.start_btn.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止检测")
        self.stop_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.stop_btn.clicked.connect(self.stop_detection)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        main_layout.addLayout(button_layout)

        # 计数显示区域
        self.count_layout = QHBoxLayout()
        self.count_labels = {}

        # 初始化计数标签
        for component in ["电阻", "电容", "LED"]:  # 根据实际模型类别修改
            label = QLabel(f"{component}: 0")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-size: 16px; font-weight: bold;")
            self.count_layout.addWidget(label)
            self.count_labels[component] = label

        main_layout.addLayout(self.count_layout)

        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("准备就绪")

    def start_detection(self):
        # 获取参数
        camera_index = self.camera_combo.currentIndex()
        resolution = self.resolution_combo.currentText()
        fps = int(self.fps_combo.currentText())

        # 发送API请求
        try:
            response = requests.post(
                f"{self.api_url}/start",
                json={
                    "camera_index": camera_index,
                    "resolution": resolution,
                    "fps": fps
                }
            )

            if response.status_code == 200:
                data = response.json()
                self.status_bar.showMessage(f"已启动 - RTMP: {data.get('rtmp_url', '')}")

                # 启动视频线程
                if self.video_thread is None:
                    self.video_thread = VideoThread(self.rtmp_url, self.api_url)
                    self.video_thread.change_pixmap.connect(self.set_image)
                    self.video_thread.update_counts.connect(self.update_count_labels)
                    self.video_thread.start()

                # 更新按钮状态
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
            else:
                self.status_bar.showMessage(f"启动失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"连接服务器失败: {str(e)}")

    def stop_detection(self):
        try:
            response = requests.get(f"{self.api_url}/stop")

            if response.status_code == 200:
                self.status_bar.showMessage("已停止检测")

                # 停止视频线程
                if self.video_thread is not None:
                    self.video_thread.stop()
                    self.video_thread = None

                # 更新按钮状态
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

                # 清空视频显示
                self.video_label.clear()
                self.video_label.setStyleSheet("background-color: black;")

            else:
                self.status_bar.showMessage(f"停止失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"连接服务器失败: {str(e)}")

    def set_image(self, image):
        """设置图像到显示标签，修复花屏问题"""
        try:
            if image is not None and not image.isNull():
                # 确保图像有效
                if image.width() > 0 and image.height() > 0:
                    # 创建QPixmap并缩放显示
                    pixmap = QPixmap.fromImage(image)
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(
                            self.video_label.width(),
                            self.video_label.height(),
                            Qt.KeepAspectRatio,
                            Qt.SmoothTransformation  # 使用平滑变换
                        )
                        self.video_label.setPixmap(scaled_pixmap)
                    else:
                        print("⚠️ QPixmap创建失败")
                else:
                    print(f"⚠️ 图像尺寸无效: {image.width()}x{image.height()}")
            else:
                print("⚠️ 接收到无效图像")
        except Exception as e:
            print(f"⚠️ 图像显示错误: {e}")

    def update_count_labels(self, counts):
        for component, count in counts.items():
            if component in self.count_labels:
                self.count_labels[component].setText(f"{component}: {count}")
            else:
                # 动态添加新组件计数
                label = QLabel(f"{component}: {count}")
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("font-size: 16px; font-weight: bold;")
                self.count_layout.addWidget(label)
                self.count_labels[component] = label

    def check_server_status(self):
        if self.video_thread and self.video_thread.isRunning():
            return

        try:
            response = requests.get(f"{self.api_url}/status", timeout=1)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'running':
                    # 自动重新连接
                    if not self.video_thread:
                        self.video_thread = VideoThread(self.rtmp_url, self.api_url)
                        self.video_thread.change_pixmap.connect(self.set_image)
                        self.video_thread.update_counts.connect(self.update_count_labels)
                        self.video_thread.start()
                        self.start_btn.setEnabled(False)
                        self.stop_btn.setEnabled(True)
                        self.status_bar.showMessage("已重新连接视频流")
        except:
            pass

    def closeEvent(self, event):
        if self.video_thread is not None:
            self.video_thread.stop()
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
