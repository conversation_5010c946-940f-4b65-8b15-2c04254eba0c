#!/usr/bin/env python3
"""
完整系统测试脚本 - 验证YOLOv8低延迟检测系统
"""

import requests
import cv2
import time
import sys

def test_server_api():
    """测试服务器API"""
    print("=" * 50)
    print("测试服务器API")
    print("=" * 50)
    
    try:
        # 测试状态API
        response = requests.get("http://localhost:5000/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器状态: {data.get('status', 'unknown')}")
        else:
            print(f"❌ 状态API失败: {response.status_code}")
            return False
        
        # 测试启动流API
        print("\n检查视频流状态...")

        # 先检查是否已有流在运行
        if data.get('status') == 'running':
            print("✅ 视频流已在运行")
            return True

        # 如果没有流在运行，启动新流
        print("启动新的视频流...")
        response = requests.post(
            "http://localhost:5000/api/start",
            json={
                "camera_index": 0,
                "resolution": "640x480",
                "fps": 30
            },
            timeout=15
        )

        if response.status_code == 200:
            data = response.json()
            print(f"✅ 视频流启动成功")
            print(f"   RTMP URL: {data.get('rtmp_url', 'unknown')}")
            return True
        elif response.status_code == 400 and "already running" in response.text:
            print("✅ 视频流已在运行")
            return True
        else:
            print(f"❌ 启动流失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

def test_rtmp_stream():
    """测试RTMP流"""
    print("\n" + "=" * 50)
    print("测试RTMP流连接")
    print("=" * 50)
    
    rtmp_url = "rtmp://localhost/live/stream"
    
    # 等待流稳定
    print("等待流稳定...")
    time.sleep(8)
    
    try:
        # 使用project3低延迟配置
        cap = cv2.VideoCapture()
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        if not cap.open(rtmp_url):
            print("❌ 无法打开RTMP流")
            return False
        
        print("✅ RTMP流连接成功")
        
        # 测试读取帧
        success_count = 0
        total_frames = 20
        
        print(f"测试读取 {total_frames} 帧...")
        
        for i in range(total_frames):
            ret, frame = cap.read()
            if ret and frame is not None:
                success_count += 1
                if i % 5 == 0:  # 每5帧输出一次
                    print(f"  帧 {i+1}: ✅ 成功 ({frame.shape})")
            else:
                print(f"  帧 {i+1}: ❌ 失败")
            
            time.sleep(0.1)  # 100ms间隔
        
        cap.release()
        
        success_rate = success_count / total_frames * 100
        print(f"\n成功率: {success_rate:.1f}% ({success_count}/{total_frames})")
        
        if success_rate >= 80:
            print("✅ RTMP流测试通过")
            return True
        else:
            print("❌ RTMP流不稳定")
            return False
            
    except Exception as e:
        print(f"❌ RTMP流测试异常: {e}")
        return False

def test_detection_api():
    """测试检测API"""
    print("\n" + "=" * 50)
    print("测试检测功能")
    print("=" * 50)
    
    try:
        # 等待检测稳定
        time.sleep(3)
        
        # 获取检测状态
        response = requests.get("http://localhost:5000/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            counts = data.get('counts', {})
            
            print(f"检测状态: {status}")
            print(f"检测计数: {counts}")
            
            if status == 'running':
                print("✅ 检测功能正常运行")
                return True
            else:
                print("❌ 检测功能未运行")
                return False
        else:
            print(f"❌ 状态API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检测API测试异常: {e}")
        return False

def stop_stream():
    """停止视频流"""
    print("\n停止视频流...")
    
    try:
        response = requests.get("http://localhost:5000/api/stop", timeout=5)
        if response.status_code == 200:
            print("✅ 视频流已停止")
            return True
        else:
            print(f"❌ 停止流失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 停止流异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 YOLOv8低延迟检测系统 - 完整测试")
    print("基于project3优化方案")
    print("=" * 60)
    
    # 测试服务器API
    if not test_server_api():
        print("\n❌ 服务器API测试失败")
        return False
    
    # 测试RTMP流
    if not test_rtmp_stream():
        print("\n❌ RTMP流测试失败")
        stop_stream()
        return False
    
    # 测试检测功能
    if not test_detection_api():
        print("\n❌ 检测功能测试失败")
        stop_stream()
        return False
    
    # 停止流
    stop_stream()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！系统工作正常")
    print("=" * 60)
    print("\n✅ 系统功能验证:")
    print("   - 摄像头采集: 正常")
    print("   - YOLO检测: 正常") 
    print("   - RTMP推流: 正常")
    print("   - 客户端拉流: 正常")
    print("   - project3低延迟优化: 已应用")
    print("\n🌐 现在可以启动客户端:")
    print("   python client/main.py")
    print("\n📊 或使用简化启动:")
    print("   python simple_start.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
