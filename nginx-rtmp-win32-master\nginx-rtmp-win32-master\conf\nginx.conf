worker_processes  1;

error_log  logs/error.log info;

events {
    worker_connections  1024;
}

rtmp {
    server {
        listen 1935;
        ping 30s;
        notify_method get;

        application live {
            live on;
            meta copy;
            allow publish 127.0.0.1;  # 只允许本机推流
            allow play all;          # 允许所有客户端播放
        }
    }
}

http {
    server {
        listen      8080;

        location / {
            root html;
        }

        location /stat {
            rtmp_stat all;
            rtmp_stat_stylesheet stat.xsl;
        }

        location /stat.xsl {
            root html;
        }

        location /hls {
            #server hls fragments
            types{
                application/vnd.apple.mpegurl m3u8;
                video/mp2t ts;
            }
            alias temp/hls;
            expires -1;
        }
    }
}
