#!/usr/bin/env python3
"""
Flask服务器测试脚本
"""

import sys
import os
import requests
import time

# 添加server目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

def test_flask_server():
    """测试Flask服务器"""
    print("测试Flask服务器连接...")
    
    # 等待服务器启动
    for i in range(10):
        try:
            response = requests.get("http://localhost:5000/api/status", timeout=2)
            if response.status_code == 200:
                print("✅ Flask服务器连接成功")
                data = response.json()
                print(f"   状态: {data.get('status', 'unknown')}")
                return True
        except Exception as e:
            print(f"尝试 {i+1}/10: {e}")
            time.sleep(1)
    
    print("❌ Flask服务器连接失败")
    return False

def test_start_stream():
    """测试启动流"""
    print("\n测试启动视频流...")
    
    try:
        response = requests.post(
            "http://localhost:5000/api/start",
            json={
                "camera_index": 0,
                "resolution": "640x480",
                "fps": 30
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 视频流启动成功")
            print(f"   RTMP URL: {data.get('rtmp_url', 'unknown')}")
            return True
        else:
            print(f"❌ 视频流启动失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 视频流启动异常: {e}")
        return False

def test_stop_stream():
    """测试停止流"""
    print("\n测试停止视频流...")
    
    try:
        response = requests.get("http://localhost:5000/api/stop", timeout=5)
        
        if response.status_code == 200:
            print("✅ 视频流停止成功")
            return True
        else:
            print(f"❌ 视频流停止失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 视频流停止异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Flask服务器API测试")
    print("=" * 50)
    
    # 测试服务器连接
    if not test_flask_server():
        print("请确保Flask服务器正在运行: python server/app.py")
        return False
    
    # 测试启动流
    if not test_start_stream():
        return False
    
    # 等待一段时间让流稳定
    print("\n等待流稳定...")
    time.sleep(5)
    
    # 测试停止流
    if not test_stop_stream():
        return False
    
    print("\n" + "=" * 50)
    print("✅ 所有API测试通过！")
    print("=" * 50)
    return True

if __name__ == "__main__":
    main()
