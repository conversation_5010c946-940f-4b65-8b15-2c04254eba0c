#!/usr/bin/env python3
"""
摄像头测试脚本 - 检测可用的摄像头设备
"""

import cv2
import sys

def test_camera(index):
    """测试指定索引的摄像头"""
    print(f"测试摄像头 {index}...")
    
    # 尝试不同的后端
    backends = [
        (cv2.CAP_DSHOW, "DirectShow"),
        (cv2.CAP_MSMF, "Media Foundation"),
        (cv2.CAP_ANY, "Any")
    ]
    
    for backend, name in backends:
        try:
            print(f"  尝试后端: {name}")
            cap = cv2.VideoCapture(index, backend)
            
            if cap.isOpened():
                # 尝试读取一帧
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"  ✅ 成功! 分辨率: {width}x{height}")
                    
                    # 显示帧
                    cv2.imshow(f"Camera {index} - {name}", frame)
                    cv2.waitKey(1000)  # 显示1秒
                    cv2.destroyAllWindows()
                    
                    cap.release()
                    return True
                else:
                    print(f"  ❌ 无法读取帧")
            else:
                print(f"  ❌ 无法打开")
                
            cap.release()
            
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("摄像头设备检测")
    print("=" * 50)
    
    available_cameras = []
    
    # 测试摄像头索引 0-5
    for i in range(6):
        if test_camera(i):
            available_cameras.append(i)
        print()
    
    print("=" * 50)
    print("检测结果:")
    if available_cameras:
        print(f"✅ 可用摄像头: {available_cameras}")
        print("建议使用摄像头索引:", available_cameras[0])
    else:
        print("❌ 未检测到可用摄像头")
        print("请检查:")
        print("1. 摄像头是否正确连接")
        print("2. 摄像头驱动是否安装")
        print("3. 摄像头是否被其他程序占用")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
