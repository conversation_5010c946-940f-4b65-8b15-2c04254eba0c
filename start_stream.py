#!/usr/bin/env python3
"""
启动视频流脚本
"""

import requests
import time

def start_stream():
    """启动视频流"""
    print("启动视频流...")
    
    try:
        response = requests.post(
            "http://localhost:5000/api/start",
            json={
                "camera_index": 0,
                "resolution": "640x480",
                "fps": 30
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 视频流启动成功")
            print(f"   RTMP URL: {data.get('rtmp_url', 'unknown')}")
            print("流将持续运行，按Ctrl+C停止...")
            
            # 持续运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n停止视频流...")
                stop_response = requests.get("http://localhost:5000/api/stop")
                if stop_response.status_code == 200:
                    print("✅ 视频流已停止")
                else:
                    print("❌ 停止视频流失败")
            
            return True
        else:
            print(f"❌ 视频流启动失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 视频流启动异常: {e}")
        return False

if __name__ == "__main__":
    start_stream()
