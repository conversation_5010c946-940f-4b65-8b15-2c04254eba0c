#!/usr/bin/env python3
"""
摄像头调试脚本
"""

import cv2
import sys
import os

# 添加server目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

def debug_camera_direct():
    """直接测试摄像头"""
    print("直接测试摄像头...")
    
    cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        return False
    
    print("✅ 摄像头打开成功")
    
    # 设置基本参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    # 读取几帧测试
    for i in range(5):
        ret, frame = cap.read()
        if ret and frame is not None:
            print(f"✅ 第{i+1}帧读取成功，大小: {frame.shape}")
        else:
            print(f"❌ 第{i+1}帧读取失败")
            cap.release()
            return False
    
    cap.release()
    print("✅ 直接摄像头测试成功")
    return True

def debug_camera_manager():
    """测试摄像头管理器"""
    print("\n测试摄像头管理器...")
    
    try:
        from utils.camera import CameraManager
        
        camera_manager = CameraManager()
        print("✅ 摄像头管理器创建成功")
        
        # 获取摄像头
        camera = camera_manager.get_camera(0, '640x480', 30)
        print("✅ 摄像头获取成功")
        
        # 等待一下让摄像头稳定
        import time
        time.sleep(1)

        # 测试读取
        for i in range(5):
            ret, frame = camera.read()
            print(f"管理器第{i+1}帧读取: ret={ret}, frame={'有效' if frame is not None else '无效'}")
            if ret and frame is not None:
                print(f"✅ 管理器第{i+1}帧读取成功，大小: {frame.shape}")
            else:
                print(f"❌ 管理器第{i+1}帧读取失败")
                # 不要立即返回，继续尝试
                time.sleep(0.2)
        
        camera_manager.release_all()
        print("✅ 摄像头管理器测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("摄像头调试测试")
    print("=" * 50)
    
    # 直接测试
    if not debug_camera_direct():
        print("❌ 直接摄像头测试失败")
        return False
    
    # 管理器测试
    if not debug_camera_manager():
        print("❌ 摄像头管理器测试失败")
        return False
    
    print("\n✅ 所有摄像头测试通过")
    return True

if __name__ == "__main__":
    main()
