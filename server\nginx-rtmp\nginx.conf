worker_processes  1;

events {
    worker_connections  1024;
}

rtmp {
    server {
        listen 1935;

        # project3超低延时优化配置
        chunk_size 128;          # 减小chunk大小降低延时
        ping 3s;
        ping_timeout 1s;
        buflen 100ms;            # 减少缓冲时间

        application live {
            live on;

            # project3低延时直播优化
            interleave on;
            wait_key on;
            wait_video on;
            sync 10ms;               # 减少同步阈值
            drop_idle_publisher 5s;

            # 访问控制
            allow publish 127.0.0.1;
            allow play all;
        }
    }
}
