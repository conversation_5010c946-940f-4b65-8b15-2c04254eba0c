#!/usr/bin/env python3
"""
服务器测试脚本 - 测试各个组件是否正常工作
"""

import sys
import os

# 添加server目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import cv2
        print("✅ OpenCV导入成功")
    except Exception as e:
        print(f"❌ OpenCV导入失败: {e}")
        return False
    
    try:
        import torch
        print(f"✅ PyTorch导入成功，版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
    except Exception as e:
        print(f"❌ PyTorch导入失败: {e}")
        return False
    
    try:
        from flask import Flask
        print("✅ Flask导入成功")
    except Exception as e:
        print(f"❌ Flask导入失败: {e}")
        return False
    
    try:
        import config
        print(f"✅ 配置模块导入成功")
        print(f"   模型路径: {config.MODEL_PATH}")
        print(f"   RTMP URL: {config.RTMP_URL}")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
    
    try:
        from utils.camera import CameraManager
        print("✅ 摄像头管理器导入成功")
    except Exception as e:
        print(f"❌ 摄像头管理器导入失败: {e}")
        return False
    
    try:
        from utils.stream import StreamManager
        print("✅ 流管理器导入成功")
    except Exception as e:
        print(f"❌ 流管理器导入失败: {e}")
        return False
    
    try:
        from utils.detection import Detector
        print("✅ 检测器导入成功")
    except Exception as e:
        print(f"❌ 检测器导入失败: {e}")
        return False
    
    return True

def test_camera():
    """测试摄像头"""
    print("\n测试摄像头...")
    
    try:
        from utils.camera import CameraManager
        camera_manager = CameraManager()
        
        # 测试摄像头0
        camera = camera_manager.get_camera(0, '640x480', 30)
        print("✅ 摄像头初始化成功")
        
        # 等待摄像头稳定并测试读取帧
        import time
        time.sleep(1)

        # 尝试读取几帧
        success_count = 0
        for i in range(3):
            ret, frame = camera.read()
            if ret and frame is not None:
                success_count += 1
                print(f"✅ 摄像头第{i+1}帧读取成功，帧大小: {frame.shape}")
            else:
                print(f"⚠️ 摄像头第{i+1}帧读取失败")
            time.sleep(0.1)

        if success_count == 0:
            print("❌ 摄像头无法读取任何帧")
            return False
        elif success_count < 3:
            print(f"⚠️ 摄像头读取不稳定，成功率: {success_count}/3")
        else:
            print("✅ 摄像头读取稳定")
        
        camera_manager.release_all()
        print("✅ 摄像头资源释放成功")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
        return False

def test_model():
    """测试模型加载"""
    print("\n测试模型加载...")
    
    try:
        import config
        from utils.detection import Detector
        
        detector = Detector(config.MODEL_PATH)
        print("✅ 模型加载成功")
        
        # 创建测试图像
        import numpy as np
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 测试检测
        result_image, detections = detector.detect(test_image)
        print(f"✅ 模型检测成功，检测到 {len(detections) if detections else 0} 个对象")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("YOLOv8服务器组件测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 模块导入测试失败")
        return False
    
    # 测试摄像头
    if not test_camera():
        print("\n❌ 摄像头测试失败")
        return False
    
    # 测试模型
    if not test_model():
        print("\n❌ 模型测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！服务器组件正常")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
