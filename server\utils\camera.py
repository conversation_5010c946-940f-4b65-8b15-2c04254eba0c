import cv2
import logging

logger = logging.getLogger(__name__)

class CameraManager:
    def __init__(self):
        self.cameras = {}

    def get_camera(self, index, resolution='640x480', fps=30):
        """获取摄像头，采用project3的超低延迟优化配置"""
        if index in self.cameras:
            return self.cameras[index]

        # 解析分辨率
        width, height = map(int, resolution.split('x'))
        logger.info(f"Initializing camera {index} with resolution {width}x{height} at {fps}fps")

        # 尝试多种摄像头后端，优先使用DirectShow (Windows)
        backends = [cv2.CAP_DSHOW, cv2.CAP_MSMF, cv2.CAP_ANY]
        cap = None

        for backend in backends:
            try:
                logger.info(f"Trying camera backend: {backend}")
                cap = cv2.VideoCapture(index, backend)
                if cap.isOpened():
                    logger.info(f"Successfully opened camera with backend: {backend}")
                    break
                else:
                    cap.release()
                    cap = None
            except Exception as e:
                logger.warning(f"Failed to open camera with backend {backend}: {e}")
                if cap:
                    cap.release()
                    cap = None

        if cap is None or not cap.isOpened():
            # 尝试不指定后端
            try:
                logger.info("Trying camera without specific backend")
                cap = cv2.VideoCapture(index)
                if not cap.isOpened():
                    raise ValueError(f"无法打开摄像头索引 {index}")
            except Exception as e:
                raise ValueError(f"无法打开摄像头索引 {index}: {e}")

        # project3超低延时优化配置
        logger.info("Applying low-latency camera settings...")

        # 基本参数设置
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        cap.set(cv2.CAP_PROP_FPS, fps)

        # 超低延时核心优化 - project3配置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区，减少延迟

        # 尝试设置MJPEG格式以提高性能
        try:
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
        except:
            logger.warning("Failed to set MJPEG format, using default")

        # 禁用自动功能以减少处理时间
        try:
            cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 禁用自动曝光
            cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # 禁用自动对焦
        except:
            logger.warning("Failed to disable auto features")

        # 性能优先设置
        try:
            cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)  # 禁用RGB转换
        except:
            pass

        # 验证摄像头是否正常工作
        ret, test_frame = cap.read()
        if not ret or test_frame is None:
            cap.release()
            raise ValueError(f"摄像头 {index} 无法读取帧，可能被其他程序占用或不兼容")

        logger.info(f"Camera {index} initialized successfully with frame size: {test_frame.shape}")

        self.cameras[index] = cap
        return cap

    def release_all(self):
        """释放所有摄像头资源"""
        for index, cap in self.cameras.items():
            try:
                cap.release()
                logger.info(f"Released camera {index}")
            except Exception as e:
                logger.error(f"Error releasing camera {index}: {e}")
        self.cameras.clear()
