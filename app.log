2025-06-09 20:46:22,378 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:46:22,378 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:46:22,378 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:46:22,385 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:46:22,385 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:07,680 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:53:07,680 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:53:07,686 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:53:07,690 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:53:07,690 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:19,167 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:19,181 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/droid-sans.css HTTP/1.1" 200 -
2025-06-09 20:53:19,397 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui.css HTTP/1.1" 200 -
2025-06-09 20:53:19,477 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-bundle.js HTTP/1.1" 200 -
2025-06-09 20:53:19,478 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1" 200 -
2025-06-09 20:53:19,557 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:19,888 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/favicon-32x32.png HTTP/1.1" 200 -
2025-06-09 20:53:55,852 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:55,884 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "[36mGET /swaggerui/droid-sans.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,108 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,172 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-bundle.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,173 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,243 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:56,570 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/favicon-32x32.png HTTP/1.1[0m" 304 -
2025-06-09 20:55:01,668 - INFO - Received status request
2025-06-09 20:55:01,668 - INFO - Current status: stopped, counts: {}
2025-06-09 20:55:01,669 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:01] "GET /api/status HTTP/1.1" 200 -
2025-06-09 20:55:03,707 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 20:55:03,707 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 20:55:03,708 - INFO - StreamThread-20250609205503 - Starting stream processing
2025-06-09 20:55:03,708 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:55:03,709 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:03] "POST /api/start HTTP/1.1" 200 -
2025-06-09 20:55:05,224 - INFO - StreamThread-20250609205503 - Camera initialized
2025-06-09 20:55:05,225 - INFO - StreamThread-20250609205503 - Streamer initialized
2025-06-09 20:55:12,239 - INFO - StreamThread-20250609205503 - Processed 100 frames, FPS: 14.26
2025-06-09 20:55:17,598 - INFO - StreamThread-20250609205503 - Processed 200 frames, FPS: 18.66
2025-06-09 20:55:22,878 - INFO - StreamThread-20250609205503 - Processed 300 frames, FPS: 18.94
2025-06-09 20:55:28,175 - INFO - StreamThread-20250609205503 - Processed 400 frames, FPS: 18.88
2025-06-09 20:55:33,455 - INFO - StreamThread-20250609205503 - Processed 500 frames, FPS: 18.94
2025-06-09 20:55:38,750 - INFO - StreamThread-20250609205503 - Processed 600 frames, FPS: 18.88
2025-06-09 20:55:44,047 - INFO - StreamThread-20250609205503 - Processed 700 frames, FPS: 18.88
2025-06-09 20:55:49,327 - INFO - StreamThread-20250609205503 - Processed 800 frames, FPS: 18.94
2025-06-10 18:09:43,704 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:09:43,704 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:09:43,704 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:09:43,710 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:09:43,710 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:10:14,968 - INFO - Received status request
2025-06-10 18:10:14,969 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:14,970 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:14] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:16,955 - INFO - Received status request
2025-06-10 18:10:16,956 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:16,956 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:16] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:18,958 - INFO - Received status request
2025-06-10 18:10:18,958 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:18,959 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:18] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:20,936 - INFO - Received status request
2025-06-10 18:10:20,936 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:20,937 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:22,942 - INFO - Received status request
2025-06-10 18:10:22,942 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:22,942 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:24,952 - INFO - Received status request
2025-06-10 18:10:24,953 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:24,953 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:26,999 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:10:26,999 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:10:27,000 - INFO - StreamThread-20250610181026 - Starting stream processing with low-latency optimization
2025-06-10 18:10:27,000 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:10:27,002 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:27] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:10:29,180 - INFO - StreamThread-20250610181026 - Camera initialized
2025-06-10 18:10:29,183 - INFO - StreamThread-20250610181026 - Streamer initialized
2025-06-10 18:10:30,185 - ERROR - StreamThread-20250610181026 - Failed to read frame from camera
2025-06-10 18:10:30,190 - INFO - StreamThread-20250610181026 - Resources released, processed 1 frames in total
2025-06-10 18:11:18,174 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:11:18,174 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:11:18,174 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:11:18,179 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:11:18,180 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:12:24,863 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:12:24,863 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:12:24,865 - INFO -  * Restarting with stat
2025-06-10 18:14:51,237 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:14:51,237 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:14:51,237 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:14:51,238 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:14:51,238 - INFO - CUDA available: True
2025-06-10 18:14:51,238 - INFO - Testing model loading...
2025-06-10 18:14:51,426 - INFO - Model loaded successfully!
2025-06-10 18:14:51,427 - INFO - Starting Flask server...
2025-06-10 18:14:51,437 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:14:51,437 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:17:23,889 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:17:23,890 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:17:23,890 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:17:23,890 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:17:23,912 - INFO - CUDA available: True
2025-06-10 18:17:23,912 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:17:23,912 - INFO - Starting Flask server...
2025-06-10 18:17:23,917 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:17:23,917 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:19:01,411 - INFO - Received status request
2025-06-10 18:19:01,412 - INFO - Current status: stopped, counts: {}
2025-06-10 18:19:01,412 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:19:11,134 - INFO - Received status request
2025-06-10 18:19:11,134 - INFO - Current status: stopped, counts: {}
2025-06-10 18:19:11,135 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:19:13,176 - INFO - Received status request
2025-06-10 18:19:13,177 - INFO - Current status: stopped, counts: {}
2025-06-10 18:19:13,177 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:19:15,206 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:19:15,207 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:19:15,207 - INFO - StreamThread-20250610181915 - Starting stream processing with low-latency optimization
2025-06-10 18:19:15,207 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:19:15,208 - INFO - StreamThread-20250610181915 - Initializing detector...
2025-06-10 18:19:15,208 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:15] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:19:18,514 - INFO - StreamThread-20250610181915 - Detector initialized successfully
2025-06-10 18:19:20,557 - INFO - StreamThread-20250610181915 - Camera initialized
2025-06-10 18:19:20,558 - INFO - StreamThread-20250610181915 - Streamer initialized
2025-06-10 18:19:21,559 - ERROR - StreamThread-20250610181915 - Failed to read frame from camera
2025-06-10 18:19:21,566 - INFO - StreamThread-20250610181915 - Resources released, processed 1 frames in total
2025-06-10 18:19:31,323 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 18:19:31,324 - WARNING - Start request rejected: stream already running
2025-06-10 18:19:31,324 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:31] "[31m[1mPOST /api/start HTTP/1.1[0m" 400 -
2025-06-10 18:19:44,599 - INFO - Received stop request
2025-06-10 18:19:44,599 - INFO - Stopping stream...
2025-06-10 18:19:44,600 - INFO - Stream thread joined successfully
2025-06-10 18:19:44,600 - INFO - Stream stopped successfully
2025-06-10 18:19:44,600 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:44] "GET /api/stop HTTP/1.1" 200 -
2025-06-10 18:19:46,583 - INFO - Received stop request
2025-06-10 18:19:46,583 - WARNING - Stop request rejected: no stream running
2025-06-10 18:19:46,584 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:46] "[31m[1mGET /api/stop HTTP/1.1[0m" 400 -
2025-06-10 18:19:55,259 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 18:19:55,260 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 18:19:55,260 - INFO - StreamThread-20250610181955 - Starting stream processing with low-latency optimization
2025-06-10 18:19:55,260 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:19:55,261 - INFO - StreamThread-20250610181955 - Camera initialized
2025-06-10 18:19:55,261 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:55] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:19:55,263 - INFO - StreamThread-20250610181955 - Streamer initialized
2025-06-10 18:19:55,263 - ERROR - StreamThread-20250610181955 - Failed to read frame from camera
2025-06-10 18:19:55,264 - INFO - StreamThread-20250610181955 - Resources released, processed 0 frames in total
2025-06-10 18:20:21,015 - INFO - Received status request
2025-06-10 18:20:21,016 - INFO - Current status: running, counts: {'C': 3}
2025-06-10 18:20:21,016 - INFO - 127.0.0.1 - - [10/Jun/2025 18:20:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:26:28,146 - INFO - Received status request
2025-06-10 18:26:28,147 - INFO - Current status: running, counts: {'C': 3}
2025-06-10 18:26:28,147 - INFO - 127.0.0.1 - - [10/Jun/2025 18:26:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:28:20,797 - INFO - Received status request
2025-06-10 18:28:20,798 - INFO - Current status: running, counts: {'C': 3}
2025-06-10 18:28:20,798 - INFO - 127.0.0.1 - - [10/Jun/2025 18:28:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:32:11,299 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:32:11,300 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:32:11,300 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:32:11,300 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:32:11,327 - INFO - CUDA available: True
2025-06-10 18:32:11,328 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:32:11,328 - INFO - Starting Flask server...
2025-06-10 18:32:11,334 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:32:11,334 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:32:15,844 - INFO - Received status request
2025-06-10 18:32:15,844 - INFO - Current status: stopped, counts: {}
2025-06-10 18:32:15,845 - INFO - 127.0.0.1 - - [10/Jun/2025 18:32:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:32:17,875 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:32:17,876 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:32:17,876 - INFO - StreamThread-20250610183217 - Starting stream processing with low-latency optimization
2025-06-10 18:32:17,876 - INFO - StreamThread-20250610183217 - Initializing detector...
2025-06-10 18:32:17,876 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:32:17,877 - INFO - 127.0.0.1 - - [10/Jun/2025 18:32:17] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:32:21,048 - INFO - StreamThread-20250610183217 - Detector initialized successfully
2025-06-10 18:32:23,086 - INFO - StreamThread-20250610183217 - Camera initialized
2025-06-10 18:32:23,088 - INFO - StreamThread-20250610183217 - Streamer initialized
2025-06-10 18:32:24,056 - ERROR - StreamThread-20250610183217 - Failed to read frame from camera
2025-06-10 18:32:24,061 - INFO - StreamThread-20250610183217 - Resources released, processed 1 frames in total
2025-06-10 18:49:26,412 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:49:26,413 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:49:26,413 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:49:26,413 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:49:26,439 - INFO - CUDA available: True
2025-06-10 18:49:26,439 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:49:26,440 - INFO - Starting Flask server...
2025-06-10 18:49:26,444 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:49:26,445 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:54:56,435 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:54:56,435 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:54:56,435 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:54:56,436 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:54:56,456 - INFO - CUDA available: True
2025-06-10 18:54:56,456 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:54:56,457 - INFO - Starting Flask server...
2025-06-10 18:54:56,461 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:54:56,461 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:56:28,038 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:56:28,038 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:56:28,038 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:56:28,038 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:56:28,063 - INFO - CUDA available: True
2025-06-10 18:56:28,064 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:56:28,064 - INFO - Starting Flask server...
2025-06-10 18:56:28,069 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:56:28,069 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:00:08,451 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:00:08,452 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:00:08,452 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:00:08,452 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:00:08,474 - INFO - CUDA available: True
2025-06-10 19:00:08,474 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:00:08,474 - INFO - Starting Flask server...
2025-06-10 19:00:08,478 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:00:08,478 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:05:02,280 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:05:02,280 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:05:02,281 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:05:02,281 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:05:02,307 - INFO - CUDA available: True
2025-06-10 19:05:02,307 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:05:02,307 - INFO - Starting Flask server...
2025-06-10 19:05:02,311 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:05:02,311 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:06:07,045 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:06:07,045 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:06:07,046 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:06:07,046 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:06:07,069 - INFO - CUDA available: True
2025-06-10 19:06:07,069 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:06:07,069 - INFO - Starting Flask server...
2025-06-10 19:06:07,073 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:06:07,073 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:06:36,679 - INFO - Received status request
2025-06-10 19:06:36,680 - INFO - Current status: stopped, counts: {}
2025-06-10 19:06:36,681 - INFO - 127.0.0.1 - - [10/Jun/2025 19:06:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:06:38,754 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:06:38,755 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:06:38,756 - INFO - StreamThread-20250610190638 - Starting stream processing with low-latency optimization
2025-06-10 19:06:38,756 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:06:38,756 - INFO - StreamThread-20250610190638 - Initializing detector...
2025-06-10 19:06:38,757 - INFO - 127.0.0.1 - - [10/Jun/2025 19:06:38] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:06:42,036 - INFO - StreamThread-20250610190638 - Detector initialized successfully
2025-06-10 19:06:42,036 - INFO - StreamThread-20250610190638 - Initializing camera 0...
2025-06-10 19:06:42,036 - INFO - Initializing camera 0 with resolution 640x480 at 30fps
2025-06-10 19:06:42,036 - INFO - Trying camera backend: 700
2025-06-10 19:06:42,219 - INFO - Successfully opened camera with backend: 700
2025-06-10 19:06:42,219 - INFO - Applying low-latency camera settings...
2025-06-10 19:06:44,947 - INFO - Camera 0 initialized successfully with frame size: (480, 640, 3)
2025-06-10 19:06:44,947 - INFO - StreamThread-20250610190638 - Camera initialized successfully
2025-06-10 19:06:44,947 - INFO - StreamThread-20250610190638 - Initializing streamer...
2025-06-10 19:06:45,050 - INFO - StreamThread-20250610190638 - Streamer initialized successfully
2025-06-10 19:06:45,050 - INFO - StreamThread-20250610190638 - Starting main processing loop...
2025-06-10 19:06:45,050 - WARNING - StreamThread-20250610190638 - Failed to read frame (attempt 1)
2025-06-10 19:06:45,805 - INFO - Received stop request
2025-06-10 19:06:45,806 - INFO - Stopping stream...
2025-06-10 19:06:47,249 - INFO - StreamThread-20250610190638 - Camera released
2025-06-10 19:06:47,250 - INFO - StreamThread-20250610190638 - Streamer released
2025-06-10 19:06:47,250 - INFO - StreamThread-20250610190638 - Resources cleaned up, processed 1 frames in total
2025-06-10 19:06:47,251 - INFO - Stream thread joined successfully
2025-06-10 19:06:47,251 - INFO - Stream stopped successfully
2025-06-10 19:06:47,251 - INFO - 127.0.0.1 - - [10/Jun/2025 19:06:47] "GET /api/stop HTTP/1.1" 200 -
2025-06-10 19:07:16,287 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:07:16,288 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:07:16,288 - INFO - StreamThread-20250610190716 - Starting stream processing with low-latency optimization
2025-06-10 19:07:16,288 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:07:16,289 - INFO - StreamThread-20250610190716 - Initializing camera 0...
2025-06-10 19:07:16,289 - INFO - 127.0.0.1 - - [10/Jun/2025 19:07:16] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:07:16,289 - INFO - StreamThread-20250610190716 - Camera initialized successfully
2025-06-10 19:07:16,290 - INFO - StreamThread-20250610190716 - Initializing streamer...
2025-06-10 19:07:16,290 - INFO - StreamThread-20250610190716 - Streamer initialized successfully
2025-06-10 19:07:16,290 - INFO - StreamThread-20250610190716 - Starting main processing loop...
2025-06-10 19:07:16,291 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 1)
2025-06-10 19:07:16,398 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 2)
2025-06-10 19:07:16,507 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 3)
2025-06-10 19:07:16,617 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 4)
2025-06-10 19:07:16,728 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 5)
2025-06-10 19:07:16,839 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 6)
2025-06-10 19:07:16,948 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 7)
2025-06-10 19:07:17,059 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 8)
2025-06-10 19:07:17,169 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 9)
2025-06-10 19:07:17,281 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 10)
2025-06-10 19:07:17,281 - ERROR - StreamThread-20250610190716 - Too many consecutive failures, stopping stream
2025-06-10 19:07:17,281 - INFO - StreamThread-20250610190716 - Camera released
2025-06-10 19:07:17,282 - INFO - StreamThread-20250610190716 - Streamer released
2025-06-10 19:07:17,282 - INFO - StreamThread-20250610190716 - Resources cleaned up, processed 0 frames in total
2025-06-10 19:08:24,952 - INFO - Received stop request
2025-06-10 19:08:24,953 - WARNING - Stop request rejected: no stream running
2025-06-10 19:08:24,953 - INFO - 127.0.0.1 - - [10/Jun/2025 19:08:24] "[31m[1mGET /api/stop HTTP/1.1[0m" 400 -
2025-06-10 19:08:36,434 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:08:36,435 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:08:36,435 - INFO - StreamThread-20250610190836 - Starting stream processing with low-latency optimization
2025-06-10 19:08:36,435 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:08:36,436 - INFO - StreamThread-20250610190836 - Initializing camera 0...
2025-06-10 19:08:36,436 - INFO - 127.0.0.1 - - [10/Jun/2025 19:08:36] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:08:36,436 - INFO - StreamThread-20250610190836 - Camera initialized successfully
2025-06-10 19:08:36,437 - INFO - StreamThread-20250610190836 - Initializing streamer...
2025-06-10 19:08:36,437 - INFO - StreamThread-20250610190836 - Streamer initialized successfully
2025-06-10 19:08:36,437 - INFO - StreamThread-20250610190836 - Starting main processing loop...
2025-06-10 19:08:36,437 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 1)
2025-06-10 19:08:36,541 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 2)
2025-06-10 19:08:36,653 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 3)
2025-06-10 19:08:36,763 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 4)
2025-06-10 19:08:36,874 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 5)
2025-06-10 19:08:36,983 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 6)
2025-06-10 19:08:37,094 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 7)
2025-06-10 19:08:37,206 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 8)
2025-06-10 19:08:37,316 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 9)
2025-06-10 19:08:37,427 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 10)
2025-06-10 19:08:37,427 - ERROR - StreamThread-20250610190836 - Too many consecutive failures, stopping stream
2025-06-10 19:08:37,427 - INFO - StreamThread-20250610190836 - Camera released
2025-06-10 19:08:37,428 - INFO - StreamThread-20250610190836 - Streamer released
2025-06-10 19:08:37,428 - INFO - StreamThread-20250610190836 - Resources cleaned up, processed 0 frames in total
2025-06-10 19:10:05,994 - INFO - Received status request
2025-06-10 19:10:05,995 - INFO - Current status: stopped, counts: {}
2025-06-10 19:10:05,995 - INFO - 127.0.0.1 - - [10/Jun/2025 19:10:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:10:19,243 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:10:19,244 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:10:19,244 - INFO - StreamThread-20250610191019 - Starting stream processing with low-latency optimization
2025-06-10 19:10:19,245 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:10:19,245 - INFO - StreamThread-20250610191019 - Initializing camera 0...
2025-06-10 19:10:19,245 - INFO - 127.0.0.1 - - [10/Jun/2025 19:10:19] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:10:19,245 - INFO - StreamThread-20250610191019 - Camera initialized successfully
2025-06-10 19:10:19,246 - INFO - StreamThread-20250610191019 - Initializing streamer...
2025-06-10 19:10:19,246 - INFO - StreamThread-20250610191019 - Streamer initialized successfully
2025-06-10 19:10:19,246 - INFO - StreamThread-20250610191019 - Starting main processing loop...
2025-06-10 19:10:19,247 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 1)
2025-06-10 19:10:19,352 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 2)
2025-06-10 19:10:19,463 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 3)
2025-06-10 19:10:19,574 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 4)
2025-06-10 19:10:19,684 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 5)
2025-06-10 19:10:19,796 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 6)
2025-06-10 19:10:19,908 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 7)
2025-06-10 19:10:20,019 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 8)
2025-06-10 19:10:20,131 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 9)
2025-06-10 19:10:20,241 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 10)
2025-06-10 19:10:20,241 - ERROR - StreamThread-20250610191019 - Too many consecutive failures, stopping stream
2025-06-10 19:10:20,241 - INFO - StreamThread-20250610191019 - Camera released
2025-06-10 19:10:20,242 - INFO - StreamThread-20250610191019 - Streamer released
2025-06-10 19:10:20,242 - INFO - StreamThread-20250610191019 - Resources cleaned up, processed 0 frames in total
2025-06-10 19:11:33,334 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:11:33,335 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:11:33,335 - INFO - StreamThread-20250610191133 - Starting stream processing with low-latency optimization
2025-06-10 19:11:33,336 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:11:33,336 - INFO - StreamThread-20250610191133 - Initializing camera 0...
2025-06-10 19:11:33,336 - INFO - 127.0.0.1 - - [10/Jun/2025 19:11:33] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:11:33,337 - INFO - StreamThread-20250610191133 - Camera initialized successfully
2025-06-10 19:11:33,337 - INFO - StreamThread-20250610191133 - Initializing streamer...
2025-06-10 19:11:33,337 - INFO - StreamThread-20250610191133 - Streamer initialized successfully
2025-06-10 19:11:33,337 - INFO - StreamThread-20250610191133 - Starting main processing loop...
2025-06-10 19:11:33,338 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 1)
2025-06-10 19:11:33,443 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 2)
2025-06-10 19:11:33,555 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 3)
2025-06-10 19:11:33,665 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 4)
2025-06-10 19:11:33,776 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 5)
2025-06-10 19:11:33,887 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 6)
2025-06-10 19:11:33,997 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 7)
2025-06-10 19:11:34,108 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 8)
2025-06-10 19:11:34,220 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 9)
2025-06-10 19:11:34,331 - WARNING - StreamThread-20250610191133 - Failed to read frame (attempt 10)
2025-06-10 19:11:34,331 - ERROR - StreamThread-20250610191133 - Too many consecutive failures, stopping stream
2025-06-10 19:11:34,331 - INFO - StreamThread-20250610191133 - Camera released
2025-06-10 19:11:34,332 - INFO - StreamThread-20250610191133 - Streamer released
2025-06-10 19:11:34,332 - INFO - StreamThread-20250610191133 - Resources cleaned up, processed 0 frames in total
2025-06-10 19:14:22,508 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:14:22,509 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:14:22,509 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:14:22,509 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:14:22,531 - INFO - CUDA available: True
2025-06-10 19:14:22,531 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:14:22,531 - INFO - Starting Flask server...
2025-06-10 19:14:22,536 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:14:22,536 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:14:43,666 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:14:43,666 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:14:43,667 - INFO - StreamThread-20250610191443 - Starting stream processing with low-latency optimization
2025-06-10 19:14:43,667 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:14:43,667 - INFO - StreamThread-20250610191443 - Initializing detector...
2025-06-10 19:14:43,668 - INFO - 127.0.0.1 - - [10/Jun/2025 19:14:43] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:14:46,900 - INFO - StreamThread-20250610191443 - Detector initialized successfully
2025-06-10 19:14:46,900 - INFO - StreamThread-20250610191443 - Initializing camera 0...
2025-06-10 19:14:46,900 - INFO - Initializing camera 0 with resolution 640x480 at 30fps
2025-06-10 19:14:46,900 - INFO - Trying camera backend: 700
2025-06-10 19:14:47,089 - INFO - Successfully opened camera with backend: 700
2025-06-10 19:14:47,090 - INFO - Applying low-latency camera settings...
2025-06-10 19:14:49,320 - WARNING - Camera 0 read attempt 1 failed, retrying...
2025-06-10 19:14:50,422 - INFO - Camera 0 read attempt 2 successful, frame size: (480, 640, 3)
2025-06-10 19:14:50,422 - INFO - Camera 0 initialized successfully
2025-06-10 19:14:50,422 - INFO - StreamThread-20250610191443 - Camera initialized successfully
2025-06-10 19:14:50,423 - INFO - StreamThread-20250610191443 - Initializing streamer...
2025-06-10 19:14:50,525 - INFO - StreamThread-20250610191443 - Streamer initialized successfully
2025-06-10 19:14:50,525 - INFO - StreamThread-20250610191443 - Starting main processing loop...
2025-06-10 19:14:50,525 - INFO - StreamThread-20250610191443 - Waiting for camera to stabilize...
2025-06-10 19:14:57,031 - INFO - StreamThread-20250610191443 - Camera stabilized, starting frame processing...
2025-06-10 19:16:40,412 - INFO - StreamThread-20250610191443 - Processed 100 frames, FPS: 0.91
2025-06-10 19:17:00,536 - INFO - Received status request
2025-06-10 19:17:00,536 - INFO - Current status: running, counts: {'C': 14, 'LED': 3}
2025-06-10 19:17:00,537 - INFO - 127.0.0.1 - - [10/Jun/2025 19:17:00] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:18:04,243 - INFO - Received status request
2025-06-10 19:18:04,243 - INFO - Current status: running, counts: {'C': 27, 'LED': 3}
2025-06-10 19:18:04,244 - INFO - 127.0.0.1 - - [10/Jun/2025 19:18:04] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:18:05,677 - INFO - Received status request
2025-06-10 19:18:05,678 - INFO - Current status: running, counts: {'C': 24, 'LED': 3}
2025-06-10 19:18:05,678 - INFO - 127.0.0.1 - - [10/Jun/2025 19:18:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:18:07,727 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:18:07,727 - WARNING - Start request rejected: stream already running
2025-06-10 19:18:07,728 - INFO - 127.0.0.1 - - [10/Jun/2025 19:18:07] "[31m[1mPOST /api/start HTTP/1.1[0m" 400 -
2025-06-10 19:18:23,680 - INFO - StreamThread-20250610191443 - Processed 200 frames, FPS: 0.97
2025-06-10 19:18:34,942 - INFO - Received status request
2025-06-10 19:18:34,942 - INFO - Current status: running, counts: {'C': 24, 'R': 1, 'LED': 2}
2025-06-10 19:18:34,942 - INFO - 127.0.0.1 - - [10/Jun/2025 19:18:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:18:40,922 - INFO - Received status request
2025-06-10 19:18:40,923 - INFO - Current status: running, counts: {'C': 24, 'LED': 4}
2025-06-10 19:18:40,923 - INFO - 127.0.0.1 - - [10/Jun/2025 19:18:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:19:05,681 - INFO - Received status request
2025-06-10 19:19:05,682 - INFO - Current status: running, counts: {'C': 9, 'LED': 8}
2025-06-10 19:19:05,682 - INFO - 127.0.0.1 - - [10/Jun/2025 19:19:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:19:36,394 - INFO - Received status request
2025-06-10 19:19:36,395 - INFO - Current status: running, counts: {'C': 11, 'LED': 5}
2025-06-10 19:19:36,395 - INFO - 127.0.0.1 - - [10/Jun/2025 19:19:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:19:46,651 - INFO - Received status request
2025-06-10 19:19:46,652 - INFO - Current status: running, counts: {'C': 12, 'LED': 4}
2025-06-10 19:19:46,652 - INFO - 127.0.0.1 - - [10/Jun/2025 19:19:46] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:19:48,711 - INFO - Received stop request
2025-06-10 19:19:48,711 - INFO - Stopping stream...
2025-06-10 19:19:49,708 - INFO - StreamThread-20250610191443 - Camera released
2025-06-10 19:19:49,711 - INFO - StreamThread-20250610191443 - Streamer released
2025-06-10 19:19:49,711 - INFO - StreamThread-20250610191443 - Resources cleaned up, processed 284 frames in total
2025-06-10 19:19:49,711 - INFO - Stream thread joined successfully
2025-06-10 19:19:49,712 - INFO - Stream stopped successfully
2025-06-10 19:19:49,712 - INFO - 127.0.0.1 - - [10/Jun/2025 19:19:49] "GET /api/stop HTTP/1.1" 200 -
2025-06-10 19:20:21,769 - INFO - Received status request
2025-06-10 19:20:21,769 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:20:21,770 - INFO - 127.0.0.1 - - [10/Jun/2025 19:20:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:24,024 - INFO - Received status request
2025-06-10 19:21:24,024 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:24,025 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:26,010 - INFO - Received status request
2025-06-10 19:21:26,010 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:26,011 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:26] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:28,031 - INFO - Received status request
2025-06-10 19:21:28,031 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:28,032 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:30,020 - INFO - Received status request
2025-06-10 19:21:30,021 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:30,021 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:32,027 - INFO - Received status request
2025-06-10 19:21:32,027 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:32,028 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:34,031 - INFO - Received status request
2025-06-10 19:21:34,031 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:34,032 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:36,038 - INFO - Received status request
2025-06-10 19:21:36,038 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:36,039 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:38,043 - INFO - Received status request
2025-06-10 19:21:38,044 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:38,044 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:38] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:40,047 - INFO - Received status request
2025-06-10 19:21:40,048 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:40,048 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:41,081 - INFO - Received status request
2025-06-10 19:21:41,082 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:41,082 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:43,071 - INFO - Received status request
2025-06-10 19:21:43,072 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:43,072 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:45,070 - INFO - Received status request
2025-06-10 19:21:45,070 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:45,071 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:47,069 - INFO - Received status request
2025-06-10 19:21:47,070 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:47,070 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:49,074 - INFO - Received status request
2025-06-10 19:21:49,074 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:49,075 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:51,080 - INFO - Received status request
2025-06-10 19:21:51,081 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:51,081 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:53,054 - INFO - Received status request
2025-06-10 19:21:53,055 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:53,055 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:55,073 - INFO - Received status request
2025-06-10 19:21:55,074 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:55,074 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:57,074 - INFO - Received status request
2025-06-10 19:21:57,074 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:57,075 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:21:59,081 - INFO - Received status request
2025-06-10 19:21:59,082 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:21:59,082 - INFO - 127.0.0.1 - - [10/Jun/2025 19:21:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:01,055 - INFO - Received status request
2025-06-10 19:22:01,055 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:01,056 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:03,075 - INFO - Received status request
2025-06-10 19:22:03,076 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:03,076 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:05,067 - INFO - Received status request
2025-06-10 19:22:05,068 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:05,068 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:07,079 - INFO - Received status request
2025-06-10 19:22:07,080 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:07,080 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:09,069 - INFO - Received status request
2025-06-10 19:22:09,069 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:09,070 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:11,085 - INFO - Received status request
2025-06-10 19:22:11,085 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:11,086 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:13,069 - INFO - Received status request
2025-06-10 19:22:13,070 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:13,070 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:15,077 - INFO - Received status request
2025-06-10 19:22:15,078 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:15,078 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:17,070 - INFO - Received status request
2025-06-10 19:22:17,071 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:17,071 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:19,078 - INFO - Received status request
2025-06-10 19:22:19,078 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:19,079 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:21,068 - INFO - Received status request
2025-06-10 19:22:21,069 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:21,069 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:23,077 - INFO - Received status request
2025-06-10 19:22:23,078 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:23,078 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:25,078 - INFO - Received status request
2025-06-10 19:22:25,079 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:25,079 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:27,083 - INFO - Received status request
2025-06-10 19:22:27,084 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:27,084 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:29,100 - INFO - Received status request
2025-06-10 19:22:29,100 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:29,101 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:31,102 - INFO - Received status request
2025-06-10 19:22:31,103 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:31,103 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:33,124 - INFO - Received status request
2025-06-10 19:22:33,125 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:33,125 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:35,116 - INFO - Received status request
2025-06-10 19:22:35,117 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:35,117 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:37,117 - INFO - Received status request
2025-06-10 19:22:37,117 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:37,118 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:39,124 - INFO - Received status request
2025-06-10 19:22:39,124 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:39,125 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:41,116 - INFO - Received status request
2025-06-10 19:22:41,117 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:41,117 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:43,124 - INFO - Received status request
2025-06-10 19:22:43,125 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:43,125 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:45,115 - INFO - Received status request
2025-06-10 19:22:45,116 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:45,116 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:47,121 - INFO - Received status request
2025-06-10 19:22:47,122 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:47,122 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:49,113 - INFO - Received status request
2025-06-10 19:22:49,113 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:49,114 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:51,119 - INFO - Received status request
2025-06-10 19:22:51,120 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:51,120 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:53,134 - INFO - Received status request
2025-06-10 19:22:53,135 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:53,135 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:55,151 - INFO - Received status request
2025-06-10 19:22:55,151 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:55,152 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:57,158 - INFO - Received status request
2025-06-10 19:22:57,158 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:57,159 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:22:59,147 - INFO - Received status request
2025-06-10 19:22:59,147 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:22:59,148 - INFO - 127.0.0.1 - - [10/Jun/2025 19:22:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:01,165 - INFO - Received status request
2025-06-10 19:23:01,165 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:01,166 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:03,163 - INFO - Received status request
2025-06-10 19:23:03,163 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:03,164 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:05,163 - INFO - Received status request
2025-06-10 19:23:05,163 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:05,164 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:07,154 - INFO - Received status request
2025-06-10 19:23:07,154 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:07,155 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:09,143 - INFO - Received status request
2025-06-10 19:23:09,144 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:09,144 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:11,148 - INFO - Received status request
2025-06-10 19:23:11,149 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:11,149 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:13,154 - INFO - Received status request
2025-06-10 19:23:13,155 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:13,155 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:15,158 - INFO - Received status request
2025-06-10 19:23:15,159 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:15,159 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:17,173 - INFO - Received status request
2025-06-10 19:23:17,174 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:17,174 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:19,163 - INFO - Received status request
2025-06-10 19:23:19,163 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:19,164 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:21,169 - INFO - Received status request
2025-06-10 19:23:21,170 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:21,170 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:23,161 - INFO - Received status request
2025-06-10 19:23:23,162 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:23,162 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:25,183 - INFO - Received status request
2025-06-10 19:23:25,183 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:25,184 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:27,179 - INFO - Received status request
2025-06-10 19:23:27,179 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:27,180 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:29,183 - INFO - Received status request
2025-06-10 19:23:29,183 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:29,184 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:31,217 - INFO - Received status request
2025-06-10 19:23:31,218 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:31,218 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:33,200 - INFO - Received status request
2025-06-10 19:23:33,200 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:33,201 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:35,191 - INFO - Received status request
2025-06-10 19:23:35,192 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:35,192 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:37,212 - INFO - Received status request
2025-06-10 19:23:37,212 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:37,213 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:39,206 - INFO - Received status request
2025-06-10 19:23:39,207 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:39,207 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:41,206 - INFO - Received status request
2025-06-10 19:23:41,207 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:41,207 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:43,196 - INFO - Received status request
2025-06-10 19:23:43,197 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:43,197 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:45,200 - INFO - Received status request
2025-06-10 19:23:45,201 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:45,201 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:47,215 - INFO - Received status request
2025-06-10 19:23:47,216 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:47,216 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:49,213 - INFO - Received status request
2025-06-10 19:23:49,214 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:49,214 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:51,211 - INFO - Received status request
2025-06-10 19:23:51,212 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:51,212 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:53,209 - INFO - Received status request
2025-06-10 19:23:53,210 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:53,210 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:55,208 - INFO - Received status request
2025-06-10 19:23:55,209 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:55,209 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:57,226 - INFO - Received status request
2025-06-10 19:23:57,227 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:57,227 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:23:59,215 - INFO - Received status request
2025-06-10 19:23:59,216 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:23:59,216 - INFO - 127.0.0.1 - - [10/Jun/2025 19:23:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:01,220 - INFO - Received status request
2025-06-10 19:24:01,221 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:01,221 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:03,225 - INFO - Received status request
2025-06-10 19:24:03,226 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:03,226 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:05,250 - INFO - Received status request
2025-06-10 19:24:05,251 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:05,251 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:07,271 - INFO - Received status request
2025-06-10 19:24:07,272 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:07,272 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:09,259 - INFO - Received status request
2025-06-10 19:24:09,260 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:09,260 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:11,268 - INFO - Received status request
2025-06-10 19:24:11,269 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:11,269 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:13,259 - INFO - Received status request
2025-06-10 19:24:13,259 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:13,260 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:15,263 - INFO - Received status request
2025-06-10 19:24:15,264 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:15,264 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:17,269 - INFO - Received status request
2025-06-10 19:24:17,270 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:17,270 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:19,277 - INFO - Received status request
2025-06-10 19:24:19,278 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:19,278 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:21,281 - INFO - Received status request
2025-06-10 19:24:21,282 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:21,282 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:23,271 - INFO - Received status request
2025-06-10 19:24:23,272 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:23,272 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:25,278 - INFO - Received status request
2025-06-10 19:24:25,278 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:25,279 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:27,289 - INFO - Received status request
2025-06-10 19:24:27,290 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:27,290 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:29,307 - INFO - Received status request
2025-06-10 19:24:29,308 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:29,308 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:31,304 - INFO - Received status request
2025-06-10 19:24:31,304 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:31,305 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:33,319 - INFO - Received status request
2025-06-10 19:24:33,320 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:33,320 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:35,323 - INFO - Received status request
2025-06-10 19:24:35,324 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:35,324 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:37,307 - INFO - Received status request
2025-06-10 19:24:37,308 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:37,308 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:39,316 - INFO - Received status request
2025-06-10 19:24:39,317 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:39,317 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:41,320 - INFO - Received status request
2025-06-10 19:24:41,321 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:41,321 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:43,341 - INFO - Received status request
2025-06-10 19:24:43,342 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:43,342 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:45,341 - INFO - Received status request
2025-06-10 19:24:45,342 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:45,342 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:47,317 - INFO - Received status request
2025-06-10 19:24:47,318 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:47,318 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:49,324 - INFO - Received status request
2025-06-10 19:24:49,325 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:49,325 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:51,338 - INFO - Received status request
2025-06-10 19:24:51,339 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:51,339 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:53,322 - INFO - Received status request
2025-06-10 19:24:53,323 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:53,323 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:55,333 - INFO - Received status request
2025-06-10 19:24:55,334 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:55,334 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:57,337 - INFO - Received status request
2025-06-10 19:24:57,338 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:57,338 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:24:59,336 - INFO - Received status request
2025-06-10 19:24:59,337 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:24:59,337 - INFO - 127.0.0.1 - - [10/Jun/2025 19:24:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:01,311 - INFO - Received status request
2025-06-10 19:25:01,311 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:01,312 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:03,306 - INFO - Received status request
2025-06-10 19:25:03,306 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:03,307 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:05,328 - INFO - Received status request
2025-06-10 19:25:05,328 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:05,329 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:07,320 - INFO - Received status request
2025-06-10 19:25:07,321 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:07,321 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:09,327 - INFO - Received status request
2025-06-10 19:25:09,328 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:09,328 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:11,317 - INFO - Received status request
2025-06-10 19:25:11,318 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:11,318 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:13,323 - INFO - Received status request
2025-06-10 19:25:13,324 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:13,324 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:15,331 - INFO - Received status request
2025-06-10 19:25:15,332 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:15,332 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:17,317 - INFO - Received status request
2025-06-10 19:25:17,318 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:17,318 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:19,329 - INFO - Received status request
2025-06-10 19:25:19,330 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:19,330 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:21,348 - INFO - Received status request
2025-06-10 19:25:21,348 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:21,349 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:23,334 - INFO - Received status request
2025-06-10 19:25:23,335 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:23,335 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:25,338 - INFO - Received status request
2025-06-10 19:25:25,339 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:25,339 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:27,359 - INFO - Received status request
2025-06-10 19:25:27,360 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:27,360 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:29,347 - INFO - Received status request
2025-06-10 19:25:29,348 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:29,348 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:31,353 - INFO - Received status request
2025-06-10 19:25:31,353 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:31,354 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:33,372 - INFO - Received status request
2025-06-10 19:25:33,372 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:33,373 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:35,365 - INFO - Received status request
2025-06-10 19:25:35,365 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:35,366 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:37,364 - INFO - Received status request
2025-06-10 19:25:37,364 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:37,365 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:39,353 - INFO - Received status request
2025-06-10 19:25:39,354 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:39,354 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:41,360 - INFO - Received status request
2025-06-10 19:25:41,361 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:41,361 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:43,364 - INFO - Received status request
2025-06-10 19:25:43,365 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:43,365 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:45,370 - INFO - Received status request
2025-06-10 19:25:45,371 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:45,371 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:47,374 - INFO - Received status request
2025-06-10 19:25:47,374 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:47,375 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:49,365 - INFO - Received status request
2025-06-10 19:25:49,366 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:49,366 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:51,355 - INFO - Received status request
2025-06-10 19:25:51,355 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:51,356 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:53,373 - INFO - Received status request
2025-06-10 19:25:53,373 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:53,374 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:55,374 - INFO - Received status request
2025-06-10 19:25:55,374 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:55,375 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:57,377 - INFO - Received status request
2025-06-10 19:25:57,378 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:57,378 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:25:59,366 - INFO - Received status request
2025-06-10 19:25:59,366 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:25:59,367 - INFO - 127.0.0.1 - - [10/Jun/2025 19:25:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:01,372 - INFO - Received status request
2025-06-10 19:26:01,372 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:01,373 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:03,378 - INFO - Received status request
2025-06-10 19:26:03,379 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:03,379 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:05,400 - INFO - Received status request
2025-06-10 19:26:05,400 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:05,401 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:07,392 - INFO - Received status request
2025-06-10 19:26:07,392 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:07,393 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:09,391 - INFO - Received status request
2025-06-10 19:26:09,392 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:09,392 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:11,413 - INFO - Received status request
2025-06-10 19:26:11,414 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:11,414 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:13,421 - INFO - Received status request
2025-06-10 19:26:13,422 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:13,422 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:15,413 - INFO - Received status request
2025-06-10 19:26:15,414 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:15,414 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:17,415 - INFO - Received status request
2025-06-10 19:26:17,415 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:17,416 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:19,422 - INFO - Received status request
2025-06-10 19:26:19,423 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:19,423 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:21,439 - INFO - Received status request
2025-06-10 19:26:21,440 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:21,441 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:23,441 - INFO - Received status request
2025-06-10 19:26:23,442 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:23,442 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:25,446 - INFO - Received status request
2025-06-10 19:26:25,447 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:25,447 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:27,452 - INFO - Received status request
2025-06-10 19:26:27,453 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:27,453 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:29,456 - INFO - Received status request
2025-06-10 19:26:29,457 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:29,457 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:31,455 - INFO - Received status request
2025-06-10 19:26:31,456 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:31,456 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:33,446 - INFO - Received status request
2025-06-10 19:26:33,446 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:33,447 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:35,448 - INFO - Received status request
2025-06-10 19:26:35,449 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:35,449 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:37,452 - INFO - Received status request
2025-06-10 19:26:37,453 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:37,453 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:39,443 - INFO - Received status request
2025-06-10 19:26:39,444 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:39,444 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:41,449 - INFO - Received status request
2025-06-10 19:26:41,450 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:41,450 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:43,454 - INFO - Received status request
2025-06-10 19:26:43,455 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:43,455 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:45,458 - INFO - Received status request
2025-06-10 19:26:45,459 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:45,459 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:47,469 - INFO - Received status request
2025-06-10 19:26:47,470 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:47,470 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:49,474 - INFO - Received status request
2025-06-10 19:26:49,475 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:49,475 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:51,466 - INFO - Received status request
2025-06-10 19:26:51,466 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:51,467 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:53,493 - INFO - Received status request
2025-06-10 19:26:53,494 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:53,494 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:55,500 - INFO - Received status request
2025-06-10 19:26:55,501 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:55,501 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:57,476 - INFO - Received status request
2025-06-10 19:26:57,477 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:57,477 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:26:59,497 - INFO - Received status request
2025-06-10 19:26:59,498 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:26:59,498 - INFO - 127.0.0.1 - - [10/Jun/2025 19:26:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:01,506 - INFO - Received status request
2025-06-10 19:27:01,507 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:01,507 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:03,510 - INFO - Received status request
2025-06-10 19:27:03,511 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:03,511 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:05,501 - INFO - Received status request
2025-06-10 19:27:05,502 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:05,502 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:07,499 - INFO - Received status request
2025-06-10 19:27:07,500 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:07,500 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:09,491 - INFO - Received status request
2025-06-10 19:27:09,492 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:09,492 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:11,499 - INFO - Received status request
2025-06-10 19:27:11,500 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:11,500 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:13,500 - INFO - Received status request
2025-06-10 19:27:13,501 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:13,501 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:15,491 - INFO - Received status request
2025-06-10 19:27:15,491 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:15,492 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:17,510 - INFO - Received status request
2025-06-10 19:27:17,511 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:17,511 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:19,501 - INFO - Received status request
2025-06-10 19:27:19,502 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:19,502 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:21,494 - INFO - Received status request
2025-06-10 19:27:21,494 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:21,495 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:23,502 - INFO - Received status request
2025-06-10 19:27:23,503 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:23,503 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:25,496 - INFO - Received status request
2025-06-10 19:27:25,497 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:25,497 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:27,507 - INFO - Received status request
2025-06-10 19:27:27,508 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:27,508 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:29,501 - INFO - Received status request
2025-06-10 19:27:29,502 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:29,502 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:31,491 - INFO - Received status request
2025-06-10 19:27:31,491 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:31,492 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:33,505 - INFO - Received status request
2025-06-10 19:27:33,506 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:33,506 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:35,502 - INFO - Received status request
2025-06-10 19:27:35,503 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:35,503 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:37,510 - INFO - Received status request
2025-06-10 19:27:37,510 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:37,511 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:39,501 - INFO - Received status request
2025-06-10 19:27:39,502 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:39,502 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:41,503 - INFO - Received status request
2025-06-10 19:27:41,504 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:41,504 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:43,511 - INFO - Received status request
2025-06-10 19:27:43,512 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:43,512 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:45,519 - INFO - Received status request
2025-06-10 19:27:45,520 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:45,520 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:47,510 - INFO - Received status request
2025-06-10 19:27:47,510 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:47,511 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:49,515 - INFO - Received status request
2025-06-10 19:27:49,516 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:49,516 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:51,520 - INFO - Received status request
2025-06-10 19:27:51,520 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:51,521 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:53,526 - INFO - Received status request
2025-06-10 19:27:53,526 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:53,527 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:55,543 - INFO - Received status request
2025-06-10 19:27:55,544 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:55,544 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:57,535 - INFO - Received status request
2025-06-10 19:27:57,536 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:57,536 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:27:59,555 - INFO - Received status request
2025-06-10 19:27:59,556 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:27:59,556 - INFO - 127.0.0.1 - - [10/Jun/2025 19:27:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:01,546 - INFO - Received status request
2025-06-10 19:28:01,547 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:01,547 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:03,563 - INFO - Received status request
2025-06-10 19:28:03,564 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:03,564 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:05,569 - INFO - Received status request
2025-06-10 19:28:05,569 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:05,570 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:07,575 - INFO - Received status request
2025-06-10 19:28:07,576 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:07,576 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:09,588 - INFO - Received status request
2025-06-10 19:28:09,589 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:09,589 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:11,575 - INFO - Received status request
2025-06-10 19:28:11,575 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:11,576 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:13,596 - INFO - Received status request
2025-06-10 19:28:13,596 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:13,596 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:15,593 - INFO - Received status request
2025-06-10 19:28:15,593 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:15,593 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:17,626 - INFO - Received status request
2025-06-10 19:28:17,627 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:17,627 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:19,616 - INFO - Received status request
2025-06-10 19:28:19,617 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:19,617 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:21,637 - INFO - Received status request
2025-06-10 19:28:21,638 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:21,638 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:23,633 - INFO - Received status request
2025-06-10 19:28:23,634 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:23,634 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:25,628 - INFO - Received status request
2025-06-10 19:28:25,629 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:25,629 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:27,618 - INFO - Received status request
2025-06-10 19:28:27,618 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:27,619 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:29,624 - INFO - Received status request
2025-06-10 19:28:29,624 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:29,625 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:31,612 - INFO - Received status request
2025-06-10 19:28:31,613 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:31,613 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:33,620 - INFO - Received status request
2025-06-10 19:28:33,621 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:33,621 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:35,625 - INFO - Received status request
2025-06-10 19:28:35,626 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:35,626 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:37,614 - INFO - Received status request
2025-06-10 19:28:37,615 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:37,615 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:39,618 - INFO - Received status request
2025-06-10 19:28:39,618 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:39,619 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:41,626 - INFO - Received status request
2025-06-10 19:28:41,627 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:41,627 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:43,630 - INFO - Received status request
2025-06-10 19:28:43,631 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:43,631 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:45,621 - INFO - Received status request
2025-06-10 19:28:45,622 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:45,622 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:47,611 - INFO - Received status request
2025-06-10 19:28:47,612 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:47,612 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:49,618 - INFO - Received status request
2025-06-10 19:28:49,619 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:49,619 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:51,626 - INFO - Received status request
2025-06-10 19:28:51,626 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:51,627 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:53,630 - INFO - Received status request
2025-06-10 19:28:53,631 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:53,631 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:55,614 - INFO - Received status request
2025-06-10 19:28:55,615 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:55,615 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:57,620 - INFO - Received status request
2025-06-10 19:28:57,620 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:57,621 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:28:59,627 - INFO - Received status request
2025-06-10 19:28:59,628 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:28:59,628 - INFO - 127.0.0.1 - - [10/Jun/2025 19:28:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:01,616 - INFO - Received status request
2025-06-10 19:29:01,617 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:01,617 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:03,622 - INFO - Received status request
2025-06-10 19:29:03,622 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:03,623 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:05,613 - INFO - Received status request
2025-06-10 19:29:05,614 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:05,614 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:07,619 - INFO - Received status request
2025-06-10 19:29:07,620 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:07,620 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:09,624 - INFO - Received status request
2025-06-10 19:29:09,624 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:09,624 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:11,614 - INFO - Received status request
2025-06-10 19:29:11,615 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:11,615 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:13,619 - INFO - Received status request
2025-06-10 19:29:13,620 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:13,620 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:15,620 - INFO - Received status request
2025-06-10 19:29:15,621 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:15,621 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:17,641 - INFO - Received status request
2025-06-10 19:29:17,642 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:17,642 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:19,601 - INFO - Received status request
2025-06-10 19:29:19,601 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:19,602 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:20,646 - INFO - Received status request
2025-06-10 19:29:20,647 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:20,647 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:22,662 - INFO - Received status request
2025-06-10 19:29:22,663 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:22,663 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:24,655 - INFO - Received status request
2025-06-10 19:29:24,655 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:24,656 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:26,668 - INFO - Received status request
2025-06-10 19:29:26,668 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:26,669 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:26] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:28,665 - INFO - Received status request
2025-06-10 19:29:28,666 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:28,666 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:30,682 - INFO - Received status request
2025-06-10 19:29:30,683 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:30,683 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:32,694 - INFO - Received status request
2025-06-10 19:29:32,695 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:32,695 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:34,709 - INFO - Received status request
2025-06-10 19:29:34,709 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:34,710 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:36,694 - INFO - Received status request
2025-06-10 19:29:36,695 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:36,695 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:38,698 - INFO - Received status request
2025-06-10 19:29:38,699 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:38,699 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:38] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:40,706 - INFO - Received status request
2025-06-10 19:29:40,706 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:40,707 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:42,694 - INFO - Received status request
2025-06-10 19:29:42,695 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:42,695 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:42] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:44,699 - INFO - Received status request
2025-06-10 19:29:44,699 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:44,699 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:44] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:46,704 - INFO - Received status request
2025-06-10 19:29:46,705 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:46,705 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:46] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:48,696 - INFO - Received status request
2025-06-10 19:29:48,697 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:48,697 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:48] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:50,702 - INFO - Received status request
2025-06-10 19:29:50,703 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:50,703 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:50] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:52,693 - INFO - Received status request
2025-06-10 19:29:52,693 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:52,694 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:52] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:54,687 - INFO - Received status request
2025-06-10 19:29:54,688 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:54,688 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:54] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:56,708 - INFO - Received status request
2025-06-10 19:29:56,709 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:56,709 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:56] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:29:58,699 - INFO - Received status request
2025-06-10 19:29:58,700 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:29:58,700 - INFO - 127.0.0.1 - - [10/Jun/2025 19:29:58] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:00,690 - INFO - Received status request
2025-06-10 19:30:00,690 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:00,691 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:00] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:02,695 - INFO - Received status request
2025-06-10 19:30:02,696 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:02,696 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:02] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:04,701 - INFO - Received status request
2025-06-10 19:30:04,701 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:04,702 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:04] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:06,703 - INFO - Received status request
2025-06-10 19:30:06,704 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:06,704 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:06] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:08,708 - INFO - Received status request
2025-06-10 19:30:08,709 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:08,709 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:08] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:10,714 - INFO - Received status request
2025-06-10 19:30:10,715 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:10,715 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:10] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:12,724 - INFO - Received status request
2025-06-10 19:30:12,724 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:12,725 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:12] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:14,712 - INFO - Received status request
2025-06-10 19:30:14,713 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:14,713 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:14] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:16,724 - INFO - Received status request
2025-06-10 19:30:16,724 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:16,725 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:16] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:18,739 - INFO - Received status request
2025-06-10 19:30:18,739 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:18,739 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:18] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:20,745 - INFO - Received status request
2025-06-10 19:30:20,746 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:20,746 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:22,753 - INFO - Received status request
2025-06-10 19:30:22,754 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:22,754 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:24,743 - INFO - Received status request
2025-06-10 19:30:24,744 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:24,744 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:26,747 - INFO - Received status request
2025-06-10 19:30:26,748 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:26,748 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:26] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:28,747 - INFO - Received status request
2025-06-10 19:30:28,748 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:28,748 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:30,750 - INFO - Received status request
2025-06-10 19:30:30,751 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:30,751 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:32,742 - INFO - Received status request
2025-06-10 19:30:32,742 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:32,743 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:33,759 - INFO - Received status request
2025-06-10 19:30:33,760 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:33,760 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:35,766 - INFO - Received status request
2025-06-10 19:30:35,766 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:35,767 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:37,771 - INFO - Received status request
2025-06-10 19:30:37,772 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:37,772 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:39,770 - INFO - Received status request
2025-06-10 19:30:39,770 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:39,771 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:41,783 - INFO - Received status request
2025-06-10 19:30:41,783 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:41,784 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:43,772 - INFO - Received status request
2025-06-10 19:30:43,773 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:43,773 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:45,780 - INFO - Received status request
2025-06-10 19:30:45,781 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:45,781 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:47,784 - INFO - Received status request
2025-06-10 19:30:47,784 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:47,785 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:49,788 - INFO - Received status request
2025-06-10 19:30:49,788 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:49,789 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:51,808 - INFO - Received status request
2025-06-10 19:30:51,809 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:51,809 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:53,830 - INFO - Received status request
2025-06-10 19:30:53,831 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:53,831 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:55,820 - INFO - Received status request
2025-06-10 19:30:55,820 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:55,821 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:57,807 - INFO - Received status request
2025-06-10 19:30:57,807 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:57,808 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:30:59,830 - INFO - Received status request
2025-06-10 19:30:59,830 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:30:59,831 - INFO - 127.0.0.1 - - [10/Jun/2025 19:30:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:01,821 - INFO - Received status request
2025-06-10 19:31:01,821 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:01,822 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:03,825 - INFO - Received status request
2025-06-10 19:31:03,825 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:03,826 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:05,830 - INFO - Received status request
2025-06-10 19:31:05,830 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:05,831 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:07,831 - INFO - Received status request
2025-06-10 19:31:07,832 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:07,832 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:09,822 - INFO - Received status request
2025-06-10 19:31:09,823 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:09,823 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:11,826 - INFO - Received status request
2025-06-10 19:31:11,827 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:11,827 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:13,817 - INFO - Received status request
2025-06-10 19:31:13,817 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:13,818 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:15,822 - INFO - Received status request
2025-06-10 19:31:15,823 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:15,823 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:17,828 - INFO - Received status request
2025-06-10 19:31:17,828 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:17,829 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:19,817 - INFO - Received status request
2025-06-10 19:31:19,818 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:19,818 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:21,825 - INFO - Received status request
2025-06-10 19:31:21,826 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:21,826 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:23,817 - INFO - Received status request
2025-06-10 19:31:23,818 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:23,818 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:23] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:25,831 - INFO - Received status request
2025-06-10 19:31:25,832 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:25,832 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:27,828 - INFO - Received status request
2025-06-10 19:31:27,828 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:27,828 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:29,841 - INFO - Received status request
2025-06-10 19:31:29,841 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:29,842 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:29] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:31,830 - INFO - Received status request
2025-06-10 19:31:31,831 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:31,831 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:33,820 - INFO - Received status request
2025-06-10 19:31:33,820 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:33,821 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:33] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:35,825 - INFO - Received status request
2025-06-10 19:31:35,825 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:35,826 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:35] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:37,835 - INFO - Received status request
2025-06-10 19:31:37,836 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:37,836 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:39,871 - INFO - Received status request
2025-06-10 19:31:39,871 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:39,871 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:39] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:41,877 - INFO - Received status request
2025-06-10 19:31:41,877 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:41,877 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:41] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:43,867 - INFO - Received status request
2025-06-10 19:31:43,867 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:43,867 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:45,881 - INFO - Received status request
2025-06-10 19:31:45,881 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:45,882 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:47,880 - INFO - Received status request
2025-06-10 19:31:47,880 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:47,880 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:47] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:49,885 - INFO - Received status request
2025-06-10 19:31:49,886 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:49,886 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:49] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:51,907 - INFO - Received status request
2025-06-10 19:31:51,908 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:51,908 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:51] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:53,896 - INFO - Received status request
2025-06-10 19:31:53,897 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:53,897 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:53] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:55,899 - INFO - Received status request
2025-06-10 19:31:55,900 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:55,900 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:55] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:57,904 - INFO - Received status request
2025-06-10 19:31:57,905 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:57,905 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:57] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:31:59,894 - INFO - Received status request
2025-06-10 19:31:59,894 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:31:59,895 - INFO - 127.0.0.1 - - [10/Jun/2025 19:31:59] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:01,909 - INFO - Received status request
2025-06-10 19:32:01,910 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:01,910 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:03,922 - INFO - Received status request
2025-06-10 19:32:03,923 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:03,923 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:03] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:05,913 - INFO - Received status request
2025-06-10 19:32:05,913 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:05,914 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:07,934 - INFO - Received status request
2025-06-10 19:32:07,935 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:07,935 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:09,941 - INFO - Received status request
2025-06-10 19:32:09,942 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:09,942 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:11,944 - INFO - Received status request
2025-06-10 19:32:11,945 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:11,945 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:13,949 - INFO - Received status request
2025-06-10 19:32:13,950 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:13,950 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:15,956 - INFO - Received status request
2025-06-10 19:32:15,957 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:15,957 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:17,972 - INFO - Received status request
2025-06-10 19:32:17,973 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:17,973 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:19,993 - INFO - Received status request
2025-06-10 19:32:19,993 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:19,994 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:21,999 - INFO - Received status request
2025-06-10 19:32:21,999 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:21,999 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:24,016 - INFO - Received status request
2025-06-10 19:32:24,017 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:24,017 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:25,990 - INFO - Received status request
2025-06-10 19:32:25,991 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:25,991 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:25] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:27,996 - INFO - Received status request
2025-06-10 19:32:27,996 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:27,997 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:30,018 - INFO - Received status request
2025-06-10 19:32:30,019 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:30,019 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:32,022 - INFO - Received status request
2025-06-10 19:32:32,023 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:32,023 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:34,028 - INFO - Received status request
2025-06-10 19:32:34,029 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:34,029 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:36,019 - INFO - Received status request
2025-06-10 19:32:36,019 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:36,020 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:38,038 - INFO - Received status request
2025-06-10 19:32:38,039 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:38,039 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:38] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:40,039 - INFO - Received status request
2025-06-10 19:32:40,040 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:40,040 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:42,030 - INFO - Received status request
2025-06-10 19:32:42,031 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:42,031 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:42] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:44,021 - INFO - Received status request
2025-06-10 19:32:44,022 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:44,022 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:44] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:46,042 - INFO - Received status request
2025-06-10 19:32:46,042 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:46,043 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:46] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:48,035 - INFO - Received status request
2025-06-10 19:32:48,036 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:48,036 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:48] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:50,028 - INFO - Received status request
2025-06-10 19:32:50,028 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:50,029 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:50] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:52,049 - INFO - Received status request
2025-06-10 19:32:52,050 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:52,050 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:52] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:54,082 - INFO - Received status request
2025-06-10 19:32:54,083 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:54,083 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:54] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:56,080 - INFO - Received status request
2025-06-10 19:32:56,080 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:56,081 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:56] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:32:58,058 - INFO - Received status request
2025-06-10 19:32:58,059 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:32:58,059 - INFO - 127.0.0.1 - - [10/Jun/2025 19:32:58] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:00,079 - INFO - Received status request
2025-06-10 19:33:00,080 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:00,080 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:00] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:02,069 - INFO - Received status request
2025-06-10 19:33:02,069 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:02,070 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:02] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:04,087 - INFO - Received status request
2025-06-10 19:33:04,088 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:04,088 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:04] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:06,088 - INFO - Received status request
2025-06-10 19:33:06,089 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:06,089 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:06] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:08,090 - INFO - Received status request
2025-06-10 19:33:08,091 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:08,091 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:08] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:10,097 - INFO - Received status request
2025-06-10 19:33:10,097 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:10,098 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:10] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:12,102 - INFO - Received status request
2025-06-10 19:33:12,102 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:12,103 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:12] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:14,092 - INFO - Received status request
2025-06-10 19:33:14,093 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:14,093 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:14] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:16,113 - INFO - Received status request
2025-06-10 19:33:16,113 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:16,114 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:16] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:18,118 - INFO - Received status request
2025-06-10 19:33:18,118 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:18,119 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:18] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:20,124 - INFO - Received status request
2025-06-10 19:33:20,124 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:20,125 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:22,115 - INFO - Received status request
2025-06-10 19:33:22,116 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:22,116 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:24,123 - INFO - Received status request
2025-06-10 19:33:24,124 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:24,124 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:26,113 - INFO - Received status request
2025-06-10 19:33:26,114 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:26,114 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:26] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:28,105 - INFO - Received status request
2025-06-10 19:33:28,105 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:28,106 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:30,126 - INFO - Received status request
2025-06-10 19:33:30,126 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:30,127 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:32,114 - INFO - Received status request
2025-06-10 19:33:32,114 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:32,115 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:34,115 - INFO - Received status request
2025-06-10 19:33:34,116 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:34,116 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:36,112 - INFO - Received status request
2025-06-10 19:33:36,113 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:36,113 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:38,100 - INFO - Received status request
2025-06-10 19:33:38,100 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:38,101 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:38] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:40,114 - INFO - Received status request
2025-06-10 19:33:40,114 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:40,115 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:42,109 - INFO - Received status request
2025-06-10 19:33:42,110 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:42,110 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:42] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:44,112 - INFO - Received status request
2025-06-10 19:33:44,113 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:44,113 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:44] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:46,119 - INFO - Received status request
2025-06-10 19:33:46,120 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:46,120 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:46] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:48,125 - INFO - Received status request
2025-06-10 19:33:48,125 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:48,126 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:48] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:50,112 - INFO - Received status request
2025-06-10 19:33:50,112 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:50,113 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:50] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:52,117 - INFO - Received status request
2025-06-10 19:33:52,118 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:52,118 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:52] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:54,122 - INFO - Received status request
2025-06-10 19:33:54,123 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:54,123 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:54] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:56,128 - INFO - Received status request
2025-06-10 19:33:56,129 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:56,129 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:56] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:33:58,136 - INFO - Received status request
2025-06-10 19:33:58,136 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:33:58,137 - INFO - 127.0.0.1 - - [10/Jun/2025 19:33:58] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:00,136 - INFO - Received status request
2025-06-10 19:34:00,136 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:00,137 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:00] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:02,120 - INFO - Received status request
2025-06-10 19:34:02,121 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:02,121 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:02] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:04,140 - INFO - Received status request
2025-06-10 19:34:04,141 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:04,141 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:04] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:06,131 - INFO - Received status request
2025-06-10 19:34:06,132 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:06,132 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:06] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:08,137 - INFO - Received status request
2025-06-10 19:34:08,137 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:08,138 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:08] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:10,130 - INFO - Received status request
2025-06-10 19:34:10,130 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:10,131 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:10] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:12,136 - INFO - Received status request
2025-06-10 19:34:12,137 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:12,137 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:12] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:14,143 - INFO - Received status request
2025-06-10 19:34:14,143 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:14,143 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:14] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:16,147 - INFO - Received status request
2025-06-10 19:34:16,148 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:16,148 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:16] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:18,151 - INFO - Received status request
2025-06-10 19:34:18,151 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:18,152 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:18] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:20,154 - INFO - Received status request
2025-06-10 19:34:20,155 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:20,155 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:22,130 - INFO - Received status request
2025-06-10 19:34:22,130 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:22,131 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:24,136 - INFO - Received status request
2025-06-10 19:34:24,137 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:24,137 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:26,153 - INFO - Received status request
2025-06-10 19:34:26,154 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:26,154 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:26] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:28,159 - INFO - Received status request
2025-06-10 19:34:28,160 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:28,160 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:30,188 - INFO - Received status request
2025-06-10 19:34:30,188 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:30,189 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:32,206 - INFO - Received status request
2025-06-10 19:34:32,206 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:32,207 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:34,208 - INFO - Received status request
2025-06-10 19:34:34,209 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:34,209 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:36,215 - INFO - Received status request
2025-06-10 19:34:36,216 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:36,216 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:38,221 - INFO - Received status request
2025-06-10 19:34:38,221 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:38,222 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:38] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:40,211 - INFO - Received status request
2025-06-10 19:34:40,212 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:40,212 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:42,242 - INFO - Received status request
2025-06-10 19:34:42,243 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:42,243 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:42] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:44,248 - INFO - Received status request
2025-06-10 19:34:44,249 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:44,249 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:44] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:46,223 - INFO - Received status request
2025-06-10 19:34:46,224 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:46,224 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:46] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:48,226 - INFO - Received status request
2025-06-10 19:34:48,226 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:48,226 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:48] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:50,247 - INFO - Received status request
2025-06-10 19:34:50,248 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:50,248 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:50] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:52,248 - INFO - Received status request
2025-06-10 19:34:52,249 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:52,249 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:52] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:54,240 - INFO - Received status request
2025-06-10 19:34:54,240 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:54,241 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:54] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:56,244 - INFO - Received status request
2025-06-10 19:34:56,244 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:56,245 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:56] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:34:58,241 - INFO - Received status request
2025-06-10 19:34:58,242 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:34:58,242 - INFO - 127.0.0.1 - - [10/Jun/2025 19:34:58] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:35:00,241 - INFO - Received status request
2025-06-10 19:35:00,242 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:35:00,242 - INFO - 127.0.0.1 - - [10/Jun/2025 19:35:00] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:35:02,242 - INFO - Received status request
2025-06-10 19:35:02,242 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:35:02,243 - INFO - 127.0.0.1 - - [10/Jun/2025 19:35:02] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:35:04,233 - INFO - Received status request
2025-06-10 19:35:04,234 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:35:04,234 - INFO - 127.0.0.1 - - [10/Jun/2025 19:35:04] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:35:06,233 - INFO - Received status request
2025-06-10 19:35:06,233 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:35:06,234 - INFO - 127.0.0.1 - - [10/Jun/2025 19:35:06] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:35:08,247 - INFO - Received status request
2025-06-10 19:35:08,248 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:35:08,248 - INFO - 127.0.0.1 - - [10/Jun/2025 19:35:08] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:35:10,247 - INFO - Received status request
2025-06-10 19:35:10,248 - INFO - Current status: stopped, counts: {'C': 27, 'LED': 8}
2025-06-10 19:35:10,248 - INFO - 127.0.0.1 - - [10/Jun/2025 19:35:10] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:36:26,847 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:36:26,847 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:36:26,847 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:36:26,847 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:36:26,870 - INFO - CUDA available: True
2025-06-10 19:36:26,870 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:36:26,870 - INFO - Starting Flask server...
2025-06-10 19:36:26,876 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:36:26,876 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:36:31,327 - INFO - Received status request
2025-06-10 19:36:31,327 - INFO - Current status: stopped, counts: {}
2025-06-10 19:36:31,328 - INFO - 127.0.0.1 - - [10/Jun/2025 19:36:31] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:36:33,410 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 19:36:33,410 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 19:36:33,411 - INFO - StreamThread-20250610193633 - Starting stream processing with low-latency optimization
2025-06-10 19:36:33,411 - INFO - StreamThread-20250610193633 - Initializing detector...
2025-06-10 19:36:33,411 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:36:33,411 - INFO - 127.0.0.1 - - [10/Jun/2025 19:36:33] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:36:36,683 - INFO - StreamThread-20250610193633 - Detector initialized successfully
2025-06-10 19:36:36,683 - INFO - StreamThread-20250610193633 - Initializing camera 0...
2025-06-10 19:36:36,683 - INFO - Initializing camera 0 with resolution 480x480 at 15fps
2025-06-10 19:36:36,683 - INFO - Trying camera backend: 700
2025-06-10 19:36:36,871 - INFO - Successfully opened camera with backend: 700
2025-06-10 19:36:36,871 - INFO - Applying low-latency camera settings...
2025-06-10 19:36:39,157 - WARNING - Camera 0 read attempt 1 failed, retrying...
2025-06-10 19:36:40,259 - INFO - Camera 0 read attempt 2 successful, frame size: (480, 640, 3)
2025-06-10 19:36:40,259 - INFO - Camera 0 initialized successfully
2025-06-10 19:36:40,259 - INFO - StreamThread-20250610193633 - Camera initialized successfully
2025-06-10 19:36:40,259 - INFO - StreamThread-20250610193633 - Initializing streamer...
2025-06-10 19:36:40,361 - INFO - StreamThread-20250610193633 - Streamer initialized successfully
2025-06-10 19:36:40,361 - INFO - StreamThread-20250610193633 - Starting main processing loop...
2025-06-10 19:36:40,361 - INFO - StreamThread-20250610193633 - Waiting for camera to stabilize...
2025-06-10 19:36:46,867 - INFO - StreamThread-20250610193633 - Camera stabilized, starting frame processing...
2025-06-10 19:37:37,390 - INFO - Received stop request
2025-06-10 19:37:37,390 - INFO - Stopping stream...
2025-06-10 19:37:37,950 - INFO - Received status request
2025-06-10 19:37:37,950 - INFO - Current status: stopped, counts: {}
2025-06-10 19:37:37,951 - INFO - 127.0.0.1 - - [10/Jun/2025 19:37:37] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:37:37,957 - INFO - StreamThread-20250610193633 - Camera released
2025-06-10 19:37:37,959 - INFO - StreamThread-20250610193633 - Streamer released
2025-06-10 19:37:37,959 - INFO - StreamThread-20250610193633 - Resources cleaned up, processed 49 frames in total
2025-06-10 19:37:37,959 - INFO - Stream thread joined successfully
2025-06-10 19:37:37,959 - INFO - Stream stopped successfully
2025-06-10 19:37:37,960 - INFO - 127.0.0.1 - - [10/Jun/2025 19:37:37] "GET /api/stop HTTP/1.1" 200 -
2025-06-10 19:38:08,999 - INFO - Received status request
2025-06-10 19:38:08,999 - INFO - Current status: stopped, counts: {}
2025-06-10 19:38:09,000 - INFO - 127.0.0.1 - - [10/Jun/2025 19:38:09] "GET /api/status HTTP/1.1" 200 -
