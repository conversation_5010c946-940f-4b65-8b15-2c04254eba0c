2025-06-09 20:46:22,378 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:46:22,378 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:46:22,378 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:46:22,385 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:46:22,385 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:07,680 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:53:07,680 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:53:07,686 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:53:07,690 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:53:07,690 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:19,167 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:19,181 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/droid-sans.css HTTP/1.1" 200 -
2025-06-09 20:53:19,397 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui.css HTTP/1.1" 200 -
2025-06-09 20:53:19,477 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-bundle.js HTTP/1.1" 200 -
2025-06-09 20:53:19,478 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1" 200 -
2025-06-09 20:53:19,557 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:19,888 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/favicon-32x32.png HTTP/1.1" 200 -
2025-06-09 20:53:55,852 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:55,884 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "[36mGET /swaggerui/droid-sans.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,108 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,172 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-bundle.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,173 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,243 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:56,570 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/favicon-32x32.png HTTP/1.1[0m" 304 -
2025-06-09 20:55:01,668 - INFO - Received status request
2025-06-09 20:55:01,668 - INFO - Current status: stopped, counts: {}
2025-06-09 20:55:01,669 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:01] "GET /api/status HTTP/1.1" 200 -
2025-06-09 20:55:03,707 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 20:55:03,707 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 20:55:03,708 - INFO - StreamThread-20250609205503 - Starting stream processing
2025-06-09 20:55:03,708 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:55:03,709 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:03] "POST /api/start HTTP/1.1" 200 -
2025-06-09 20:55:05,224 - INFO - StreamThread-20250609205503 - Camera initialized
2025-06-09 20:55:05,225 - INFO - StreamThread-20250609205503 - Streamer initialized
2025-06-09 20:55:12,239 - INFO - StreamThread-20250609205503 - Processed 100 frames, FPS: 14.26
2025-06-09 20:55:17,598 - INFO - StreamThread-20250609205503 - Processed 200 frames, FPS: 18.66
2025-06-09 20:55:22,878 - INFO - StreamThread-20250609205503 - Processed 300 frames, FPS: 18.94
2025-06-09 20:55:28,175 - INFO - StreamThread-20250609205503 - Processed 400 frames, FPS: 18.88
2025-06-09 20:55:33,455 - INFO - StreamThread-20250609205503 - Processed 500 frames, FPS: 18.94
2025-06-09 20:55:38,750 - INFO - StreamThread-20250609205503 - Processed 600 frames, FPS: 18.88
2025-06-09 20:55:44,047 - INFO - StreamThread-20250609205503 - Processed 700 frames, FPS: 18.88
2025-06-09 20:55:49,327 - INFO - StreamThread-20250609205503 - Processed 800 frames, FPS: 18.94
2025-06-10 18:09:43,704 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:09:43,704 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:09:43,704 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:09:43,710 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:09:43,710 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:10:14,968 - INFO - Received status request
2025-06-10 18:10:14,969 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:14,970 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:14] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:16,955 - INFO - Received status request
2025-06-10 18:10:16,956 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:16,956 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:16] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:18,958 - INFO - Received status request
2025-06-10 18:10:18,958 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:18,959 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:18] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:20,936 - INFO - Received status request
2025-06-10 18:10:20,936 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:20,937 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:22,942 - INFO - Received status request
2025-06-10 18:10:22,942 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:22,942 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:24,952 - INFO - Received status request
2025-06-10 18:10:24,953 - INFO - Current status: stopped, counts: {}
2025-06-10 18:10:24,953 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:10:26,999 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:10:26,999 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:10:27,000 - INFO - StreamThread-20250610181026 - Starting stream processing with low-latency optimization
2025-06-10 18:10:27,000 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:10:27,002 - INFO - 127.0.0.1 - - [10/Jun/2025 18:10:27] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:10:29,180 - INFO - StreamThread-20250610181026 - Camera initialized
2025-06-10 18:10:29,183 - INFO - StreamThread-20250610181026 - Streamer initialized
2025-06-10 18:10:30,185 - ERROR - StreamThread-20250610181026 - Failed to read frame from camera
2025-06-10 18:10:30,190 - INFO - StreamThread-20250610181026 - Resources released, processed 1 frames in total
2025-06-10 18:11:18,174 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:11:18,174 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:11:18,174 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:11:18,179 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:11:18,180 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:12:24,863 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:12:24,863 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:12:24,865 - INFO -  * Restarting with stat
2025-06-10 18:14:51,237 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:14:51,237 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:14:51,237 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:14:51,238 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:14:51,238 - INFO - CUDA available: True
2025-06-10 18:14:51,238 - INFO - Testing model loading...
2025-06-10 18:14:51,426 - INFO - Model loaded successfully!
2025-06-10 18:14:51,427 - INFO - Starting Flask server...
2025-06-10 18:14:51,437 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:14:51,437 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:17:23,889 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:17:23,890 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:17:23,890 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:17:23,890 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:17:23,912 - INFO - CUDA available: True
2025-06-10 18:17:23,912 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:17:23,912 - INFO - Starting Flask server...
2025-06-10 18:17:23,917 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:17:23,917 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:19:01,411 - INFO - Received status request
2025-06-10 18:19:01,412 - INFO - Current status: stopped, counts: {}
2025-06-10 18:19:01,412 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:01] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:19:11,134 - INFO - Received status request
2025-06-10 18:19:11,134 - INFO - Current status: stopped, counts: {}
2025-06-10 18:19:11,135 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:11] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:19:13,176 - INFO - Received status request
2025-06-10 18:19:13,177 - INFO - Current status: stopped, counts: {}
2025-06-10 18:19:13,177 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:13] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:19:15,206 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:19:15,207 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:19:15,207 - INFO - StreamThread-20250610181915 - Starting stream processing with low-latency optimization
2025-06-10 18:19:15,207 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:19:15,208 - INFO - StreamThread-20250610181915 - Initializing detector...
2025-06-10 18:19:15,208 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:15] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:19:18,514 - INFO - StreamThread-20250610181915 - Detector initialized successfully
2025-06-10 18:19:20,557 - INFO - StreamThread-20250610181915 - Camera initialized
2025-06-10 18:19:20,558 - INFO - StreamThread-20250610181915 - Streamer initialized
2025-06-10 18:19:21,559 - ERROR - StreamThread-20250610181915 - Failed to read frame from camera
2025-06-10 18:19:21,566 - INFO - StreamThread-20250610181915 - Resources released, processed 1 frames in total
2025-06-10 18:19:31,323 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 18:19:31,324 - WARNING - Start request rejected: stream already running
2025-06-10 18:19:31,324 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:31] "[31m[1mPOST /api/start HTTP/1.1[0m" 400 -
2025-06-10 18:19:44,599 - INFO - Received stop request
2025-06-10 18:19:44,599 - INFO - Stopping stream...
2025-06-10 18:19:44,600 - INFO - Stream thread joined successfully
2025-06-10 18:19:44,600 - INFO - Stream stopped successfully
2025-06-10 18:19:44,600 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:44] "GET /api/stop HTTP/1.1" 200 -
2025-06-10 18:19:46,583 - INFO - Received stop request
2025-06-10 18:19:46,583 - WARNING - Stop request rejected: no stream running
2025-06-10 18:19:46,584 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:46] "[31m[1mGET /api/stop HTTP/1.1[0m" 400 -
2025-06-10 18:19:55,259 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 18:19:55,260 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 18:19:55,260 - INFO - StreamThread-20250610181955 - Starting stream processing with low-latency optimization
2025-06-10 18:19:55,260 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:19:55,261 - INFO - StreamThread-20250610181955 - Camera initialized
2025-06-10 18:19:55,261 - INFO - 127.0.0.1 - - [10/Jun/2025 18:19:55] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:19:55,263 - INFO - StreamThread-20250610181955 - Streamer initialized
2025-06-10 18:19:55,263 - ERROR - StreamThread-20250610181955 - Failed to read frame from camera
2025-06-10 18:19:55,264 - INFO - StreamThread-20250610181955 - Resources released, processed 0 frames in total
2025-06-10 18:20:21,015 - INFO - Received status request
2025-06-10 18:20:21,016 - INFO - Current status: running, counts: {'C': 3}
2025-06-10 18:20:21,016 - INFO - 127.0.0.1 - - [10/Jun/2025 18:20:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:26:28,146 - INFO - Received status request
2025-06-10 18:26:28,147 - INFO - Current status: running, counts: {'C': 3}
2025-06-10 18:26:28,147 - INFO - 127.0.0.1 - - [10/Jun/2025 18:26:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:28:20,797 - INFO - Received status request
2025-06-10 18:28:20,798 - INFO - Current status: running, counts: {'C': 3}
2025-06-10 18:28:20,798 - INFO - 127.0.0.1 - - [10/Jun/2025 18:28:20] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:32:11,299 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:32:11,300 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:32:11,300 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:32:11,300 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:32:11,327 - INFO - CUDA available: True
2025-06-10 18:32:11,328 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:32:11,328 - INFO - Starting Flask server...
2025-06-10 18:32:11,334 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:32:11,334 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:32:15,844 - INFO - Received status request
2025-06-10 18:32:15,844 - INFO - Current status: stopped, counts: {}
2025-06-10 18:32:15,845 - INFO - 127.0.0.1 - - [10/Jun/2025 18:32:15] "GET /api/status HTTP/1.1" 200 -
2025-06-10 18:32:17,875 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 18:32:17,876 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 18:32:17,876 - INFO - StreamThread-20250610183217 - Starting stream processing with low-latency optimization
2025-06-10 18:32:17,876 - INFO - StreamThread-20250610183217 - Initializing detector...
2025-06-10 18:32:17,876 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:32:17,877 - INFO - 127.0.0.1 - - [10/Jun/2025 18:32:17] "POST /api/start HTTP/1.1" 200 -
2025-06-10 18:32:21,048 - INFO - StreamThread-20250610183217 - Detector initialized successfully
2025-06-10 18:32:23,086 - INFO - StreamThread-20250610183217 - Camera initialized
2025-06-10 18:32:23,088 - INFO - StreamThread-20250610183217 - Streamer initialized
2025-06-10 18:32:24,056 - ERROR - StreamThread-20250610183217 - Failed to read frame from camera
2025-06-10 18:32:24,061 - INFO - StreamThread-20250610183217 - Resources released, processed 1 frames in total
2025-06-10 18:49:26,412 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:49:26,413 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:49:26,413 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:49:26,413 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:49:26,439 - INFO - CUDA available: True
2025-06-10 18:49:26,439 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:49:26,440 - INFO - Starting Flask server...
2025-06-10 18:49:26,444 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:49:26,445 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:54:56,435 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:54:56,435 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:54:56,435 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:54:56,436 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:54:56,456 - INFO - CUDA available: True
2025-06-10 18:54:56,456 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:54:56,457 - INFO - Starting Flask server...
2025-06-10 18:54:56,461 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:54:56,461 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 18:56:28,038 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 18:56:28,038 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 18:56:28,038 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 18:56:28,038 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 18:56:28,063 - INFO - CUDA available: True
2025-06-10 18:56:28,064 - INFO - Model will be loaded when first stream starts...
2025-06-10 18:56:28,064 - INFO - Starting Flask server...
2025-06-10 18:56:28,069 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 18:56:28,069 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:00:08,451 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:00:08,452 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:00:08,452 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:00:08,452 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:00:08,474 - INFO - CUDA available: True
2025-06-10 19:00:08,474 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:00:08,474 - INFO - Starting Flask server...
2025-06-10 19:00:08,478 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:00:08,478 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:05:02,280 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:05:02,280 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:05:02,281 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:05:02,281 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:05:02,307 - INFO - CUDA available: True
2025-06-10 19:05:02,307 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:05:02,307 - INFO - Starting Flask server...
2025-06-10 19:05:02,311 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:05:02,311 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:06:07,045 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-10 19:06:07,045 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:06:07,046 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 19:06:07,046 - INFO - PyTorch version: 1.9.0+cu111
2025-06-10 19:06:07,069 - INFO - CUDA available: True
2025-06-10 19:06:07,069 - INFO - Model will be loaded when first stream starts...
2025-06-10 19:06:07,069 - INFO - Starting Flask server...
2025-06-10 19:06:07,073 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 19:06:07,073 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 19:06:36,679 - INFO - Received status request
2025-06-10 19:06:36,680 - INFO - Current status: stopped, counts: {}
2025-06-10 19:06:36,681 - INFO - 127.0.0.1 - - [10/Jun/2025 19:06:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:06:38,754 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:06:38,755 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:06:38,756 - INFO - StreamThread-20250610190638 - Starting stream processing with low-latency optimization
2025-06-10 19:06:38,756 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:06:38,756 - INFO - StreamThread-20250610190638 - Initializing detector...
2025-06-10 19:06:38,757 - INFO - 127.0.0.1 - - [10/Jun/2025 19:06:38] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:06:42,036 - INFO - StreamThread-20250610190638 - Detector initialized successfully
2025-06-10 19:06:42,036 - INFO - StreamThread-20250610190638 - Initializing camera 0...
2025-06-10 19:06:42,036 - INFO - Initializing camera 0 with resolution 640x480 at 30fps
2025-06-10 19:06:42,036 - INFO - Trying camera backend: 700
2025-06-10 19:06:42,219 - INFO - Successfully opened camera with backend: 700
2025-06-10 19:06:42,219 - INFO - Applying low-latency camera settings...
2025-06-10 19:06:44,947 - INFO - Camera 0 initialized successfully with frame size: (480, 640, 3)
2025-06-10 19:06:44,947 - INFO - StreamThread-20250610190638 - Camera initialized successfully
2025-06-10 19:06:44,947 - INFO - StreamThread-20250610190638 - Initializing streamer...
2025-06-10 19:06:45,050 - INFO - StreamThread-20250610190638 - Streamer initialized successfully
2025-06-10 19:06:45,050 - INFO - StreamThread-20250610190638 - Starting main processing loop...
2025-06-10 19:06:45,050 - WARNING - StreamThread-20250610190638 - Failed to read frame (attempt 1)
2025-06-10 19:06:45,805 - INFO - Received stop request
2025-06-10 19:06:45,806 - INFO - Stopping stream...
2025-06-10 19:06:47,249 - INFO - StreamThread-20250610190638 - Camera released
2025-06-10 19:06:47,250 - INFO - StreamThread-20250610190638 - Streamer released
2025-06-10 19:06:47,250 - INFO - StreamThread-20250610190638 - Resources cleaned up, processed 1 frames in total
2025-06-10 19:06:47,251 - INFO - Stream thread joined successfully
2025-06-10 19:06:47,251 - INFO - Stream stopped successfully
2025-06-10 19:06:47,251 - INFO - 127.0.0.1 - - [10/Jun/2025 19:06:47] "GET /api/stop HTTP/1.1" 200 -
2025-06-10 19:07:16,287 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:07:16,288 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:07:16,288 - INFO - StreamThread-20250610190716 - Starting stream processing with low-latency optimization
2025-06-10 19:07:16,288 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:07:16,289 - INFO - StreamThread-20250610190716 - Initializing camera 0...
2025-06-10 19:07:16,289 - INFO - 127.0.0.1 - - [10/Jun/2025 19:07:16] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:07:16,289 - INFO - StreamThread-20250610190716 - Camera initialized successfully
2025-06-10 19:07:16,290 - INFO - StreamThread-20250610190716 - Initializing streamer...
2025-06-10 19:07:16,290 - INFO - StreamThread-20250610190716 - Streamer initialized successfully
2025-06-10 19:07:16,290 - INFO - StreamThread-20250610190716 - Starting main processing loop...
2025-06-10 19:07:16,291 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 1)
2025-06-10 19:07:16,398 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 2)
2025-06-10 19:07:16,507 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 3)
2025-06-10 19:07:16,617 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 4)
2025-06-10 19:07:16,728 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 5)
2025-06-10 19:07:16,839 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 6)
2025-06-10 19:07:16,948 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 7)
2025-06-10 19:07:17,059 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 8)
2025-06-10 19:07:17,169 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 9)
2025-06-10 19:07:17,281 - WARNING - StreamThread-20250610190716 - Failed to read frame (attempt 10)
2025-06-10 19:07:17,281 - ERROR - StreamThread-20250610190716 - Too many consecutive failures, stopping stream
2025-06-10 19:07:17,281 - INFO - StreamThread-20250610190716 - Camera released
2025-06-10 19:07:17,282 - INFO - StreamThread-20250610190716 - Streamer released
2025-06-10 19:07:17,282 - INFO - StreamThread-20250610190716 - Resources cleaned up, processed 0 frames in total
2025-06-10 19:08:24,952 - INFO - Received stop request
2025-06-10 19:08:24,953 - WARNING - Stop request rejected: no stream running
2025-06-10 19:08:24,953 - INFO - 127.0.0.1 - - [10/Jun/2025 19:08:24] "[31m[1mGET /api/stop HTTP/1.1[0m" 400 -
2025-06-10 19:08:36,434 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:08:36,435 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:08:36,435 - INFO - StreamThread-20250610190836 - Starting stream processing with low-latency optimization
2025-06-10 19:08:36,435 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:08:36,436 - INFO - StreamThread-20250610190836 - Initializing camera 0...
2025-06-10 19:08:36,436 - INFO - 127.0.0.1 - - [10/Jun/2025 19:08:36] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:08:36,436 - INFO - StreamThread-20250610190836 - Camera initialized successfully
2025-06-10 19:08:36,437 - INFO - StreamThread-20250610190836 - Initializing streamer...
2025-06-10 19:08:36,437 - INFO - StreamThread-20250610190836 - Streamer initialized successfully
2025-06-10 19:08:36,437 - INFO - StreamThread-20250610190836 - Starting main processing loop...
2025-06-10 19:08:36,437 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 1)
2025-06-10 19:08:36,541 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 2)
2025-06-10 19:08:36,653 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 3)
2025-06-10 19:08:36,763 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 4)
2025-06-10 19:08:36,874 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 5)
2025-06-10 19:08:36,983 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 6)
2025-06-10 19:08:37,094 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 7)
2025-06-10 19:08:37,206 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 8)
2025-06-10 19:08:37,316 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 9)
2025-06-10 19:08:37,427 - WARNING - StreamThread-20250610190836 - Failed to read frame (attempt 10)
2025-06-10 19:08:37,427 - ERROR - StreamThread-20250610190836 - Too many consecutive failures, stopping stream
2025-06-10 19:08:37,427 - INFO - StreamThread-20250610190836 - Camera released
2025-06-10 19:08:37,428 - INFO - StreamThread-20250610190836 - Streamer released
2025-06-10 19:08:37,428 - INFO - StreamThread-20250610190836 - Resources cleaned up, processed 0 frames in total
2025-06-10 19:10:05,994 - INFO - Received status request
2025-06-10 19:10:05,995 - INFO - Current status: stopped, counts: {}
2025-06-10 19:10:05,995 - INFO - 127.0.0.1 - - [10/Jun/2025 19:10:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 19:10:19,243 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x480', 'fps': 30}
2025-06-10 19:10:19,244 - INFO - Starting stream with params - camera: 0, resolution: 640x480, fps: 30
2025-06-10 19:10:19,244 - INFO - StreamThread-20250610191019 - Starting stream processing with low-latency optimization
2025-06-10 19:10:19,245 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 19:10:19,245 - INFO - StreamThread-20250610191019 - Initializing camera 0...
2025-06-10 19:10:19,245 - INFO - 127.0.0.1 - - [10/Jun/2025 19:10:19] "POST /api/start HTTP/1.1" 200 -
2025-06-10 19:10:19,245 - INFO - StreamThread-20250610191019 - Camera initialized successfully
2025-06-10 19:10:19,246 - INFO - StreamThread-20250610191019 - Initializing streamer...
2025-06-10 19:10:19,246 - INFO - StreamThread-20250610191019 - Streamer initialized successfully
2025-06-10 19:10:19,246 - INFO - StreamThread-20250610191019 - Starting main processing loop...
2025-06-10 19:10:19,247 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 1)
2025-06-10 19:10:19,352 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 2)
2025-06-10 19:10:19,463 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 3)
2025-06-10 19:10:19,574 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 4)
2025-06-10 19:10:19,684 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 5)
2025-06-10 19:10:19,796 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 6)
2025-06-10 19:10:19,908 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 7)
2025-06-10 19:10:20,019 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 8)
2025-06-10 19:10:20,131 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 9)
2025-06-10 19:10:20,241 - WARNING - StreamThread-20250610191019 - Failed to read frame (attempt 10)
2025-06-10 19:10:20,241 - ERROR - StreamThread-20250610191019 - Too many consecutive failures, stopping stream
2025-06-10 19:10:20,241 - INFO - StreamThread-20250610191019 - Camera released
2025-06-10 19:10:20,242 - INFO - StreamThread-20250610191019 - Streamer released
2025-06-10 19:10:20,242 - INFO - StreamThread-20250610191019 - Resources cleaned up, processed 0 frames in total
