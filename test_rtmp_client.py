#!/usr/bin/env python3
"""
RTMP客户端测试脚本 - 测试project3优化的低延迟连接
"""

import cv2
import time
import sys

def test_rtmp_connection():
    """测试RTMP连接"""
    rtmp_url = "rtmp://localhost/live/stream"
    print(f"测试RTMP连接: {rtmp_url}")
    
    # project3低延迟连接方法
    connection_methods = [
        ("直接连接", lambda: cv2.VideoCapture(rtmp_url)),
        ("低延迟参数", lambda: create_low_latency_capture(rtmp_url)),
        ("FFmpeg后端", lambda: cv2.VideoCapture(rtmp_url, cv2.CAP_FFMPEG))
    ]
    
    for method_name, method_func in connection_methods:
        print(f"\n尝试方法: {method_name}")
        
        try:
            cap = method_func()
            
            if cap.isOpened():
                print("✅ 连接成功")
                
                # 测试读取帧
                frame_count = 0
                success_count = 0
                
                for i in range(10):
                    ret, frame = cap.read()
                    frame_count += 1
                    
                    if ret and frame is not None:
                        success_count += 1
                        print(f"  帧 {i+1}: ✅ 成功 ({frame.shape})")
                    else:
                        print(f"  帧 {i+1}: ❌ 失败")
                    
                    time.sleep(0.1)
                
                cap.release()
                
                success_rate = success_count / frame_count * 100
                print(f"成功率: {success_rate:.1f}% ({success_count}/{frame_count})")
                
                if success_rate >= 50:
                    print(f"✅ {method_name} 方法可用")
                    return True
                else:
                    print(f"❌ {method_name} 方法不稳定")
            else:
                print("❌ 连接失败")
                cap.release()
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    return False

def create_low_latency_capture(rtmp_url):
    """创建project3低延迟VideoCapture"""
    cap = cv2.VideoCapture()
    
    # project3低延迟参数
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    # 打开RTMP流
    if cap.open(rtmp_url):
        return cap
    else:
        cap.release()
        raise Exception("无法打开RTMP流")

def test_display_stream():
    """测试显示RTMP流"""
    rtmp_url = "rtmp://localhost/live/stream"
    print(f"\n测试显示RTMP流: {rtmp_url}")
    print("按 'q' 键退出显示")
    
    try:
        cap = create_low_latency_capture(rtmp_url)
        
        if not cap.isOpened():
            print("❌ 无法打开RTMP流")
            return False
        
        print("✅ RTMP流打开成功，开始显示...")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            
            if ret and frame is not None:
                frame_count += 1
                
                # 显示帧率信息
                elapsed = time.time() - start_time
                if elapsed > 0:
                    fps = frame_count / elapsed
                    cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    cv2.putText(frame, f"Frame: {frame_count}", (10, 70), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.imshow("RTMP Stream", frame)
                
                # 按'q'退出
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            else:
                print("⚠️ 读取帧失败")
                time.sleep(0.1)
        
        cap.release()
        cv2.destroyAllWindows()
        
        elapsed = time.time() - start_time
        avg_fps = frame_count / elapsed if elapsed > 0 else 0
        print(f"\n统计信息:")
        print(f"  总帧数: {frame_count}")
        print(f"  运行时间: {elapsed:.1f}秒")
        print(f"  平均帧率: {avg_fps:.1f} FPS")
        
        return True
        
    except Exception as e:
        print(f"❌ 显示流异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("RTMP客户端测试 - project3低延迟优化")
    print("=" * 50)
    
    # 测试连接
    if not test_rtmp_connection():
        print("\n❌ 所有连接方法都失败了")
        print("请检查:")
        print("1. RTMP服务器是否运行 (Nginx)")
        print("2. 视频流是否正在推送")
        print("3. 网络连接是否正常")
        return False
    
    # 询问是否显示流
    try:
        choice = input("\n是否显示视频流? (y/n): ").lower().strip()
        if choice == 'y':
            test_display_stream()
    except KeyboardInterrupt:
        print("\n用户取消")
    
    print("\n测试完成")
    return True

if __name__ == "__main__":
    main()
